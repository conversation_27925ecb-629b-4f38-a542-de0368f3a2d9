#!/usr/bin/env python3
"""
测试学生模型的基础功能
验证模型架构、参数数量、前向传播等
"""

import torch
import torch.nn as nn
import numpy as np
import time
from student_slr_network import StudentSLRModel
from slr_network import SLRModel


def count_parameters(model):
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def test_model_creation():
    """测试模型创建"""
    print("=" * 60)
    print("测试1: 学生模型创建")
    print("=" * 60)
    
    try:
        # 学生模型配置
        student_config = {
            'num_classes': 1086,
            'c2d_type': 'student',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 512,
            'weight_norm': True,
            'share_classifier': True,
            'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 0.0}
        }
        
        print(f"🔧 创建学生模型，配置: {student_config}")
        student_model = StudentSLRModel(**student_config)
        student_params = count_parameters(student_model)
        
        print(f"✅ 学生模型创建成功")
        print(f"📊 学生模型参数数量: {student_params:,}")
        
        # 尝试创建教师模型进行对比
        try:
            teacher_config = {
                'num_classes': 1086,
                'c2d_type': 'resnet18',
                'conv_type': 2,
                'use_bn': True,
                'hidden_size': 1024,
                'weight_norm': True,
                'share_classifier': True,
                'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 25.0}
            }
            teacher_model = SLRModel(**teacher_config)
            teacher_params = count_parameters(teacher_model)
            
            compression_ratio = (1 - student_params / teacher_params) * 100
            print(f"📊 教师模型参数数量: {teacher_params:,}")
            print(f"🎯 参数压缩率: {compression_ratio:.1f}%")
            
        except Exception as e:
            print(f"⚠️  无法创建教师模型进行对比: {e}")
        
        return student_model
        
    except Exception as e:
        print(f"❌ 学生模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_forward_pass(model):
    """测试前向传播"""
    print("\n" + "=" * 60)
    print("测试2: 前向传播")
    print("=" * 60)
    
    if model is None:
        print("❌ 模型为空，跳过前向传播测试")
        return False
    
    try:
        # 创建测试数据
        batch_size = 2
        seq_len = 8
        height, width = 224, 224
        
        # 输入视频数据
        videos = torch.randn(batch_size, 3, seq_len, height, width)
        video_lengths = torch.tensor([seq_len, seq_len])
        
        print(f"📥 输入形状: {videos.shape}")
        print(f"📏 视频长度: {video_lengths}")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            start_time = time.time()
            outputs = model(videos, video_lengths)
            end_time = time.time()
        
        # 检查输出
        print(f"✅ 前向传播成功")
        print(f"⏱️  推理时间: {(end_time - start_time) * 1000:.2f} ms")
        print(f"📤 输出键: {list(outputs.keys())}")
        
        # 检查输出形状
        if "sequence_logits" in outputs and outputs["sequence_logits"] is not None:
            seq_shape = outputs["sequence_logits"].shape
            print(f"📊 序列输出形状: {seq_shape}")
            
        if "conv_logits" in outputs and outputs["conv_logits"] is not None:
            conv_shape = outputs["conv_logits"].shape
            print(f"📊 卷积输出形状: {conv_shape}")
            
        if "feat_len" in outputs:
            feat_len = outputs["feat_len"]
            print(f"📏 特征长度: {feat_len}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_mode(model):
    """测试训练模式"""
    print("\n" + "=" * 60)
    print("测试3: 训练模式")
    print("=" * 60)
    
    if model is None:
        print("❌ 模型为空，跳过训练模式测试")
        return False
    
    try:
        # 创建测试数据
        batch_size = 2
        seq_len = 8
        height, width = 224, 224
        
        videos = torch.randn(batch_size, 3, seq_len, height, width)
        video_lengths = torch.tensor([seq_len, seq_len])
        labels = torch.randint(1, 1086, (batch_size, 10))
        label_lengths = torch.tensor([10, 8])
        
        print(f"📥 训练数据形状:")
        print(f"   视频: {videos.shape}")
        print(f"   标签: {labels.shape}")
        print(f"   标签长度: {label_lengths}")
        
        # 训练模式前向传播
        model.train()
        outputs = model(videos, video_lengths, labels, label_lengths)
        
        # 计算损失
        loss = model.criterion_calculation(outputs, labels, label_lengths)
        
        print(f"✅ 训练模式前向传播成功")
        print(f"📊 损失值: {loss:.4f}")
        
        # 测试反向传播
        loss.backward()
        print(f"✅ 反向传播成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_components(model):
    """测试模型组件"""
    print("\n" + "=" * 60)
    print("测试4: 模型组件分析")
    print("=" * 60)
    
    if model is None:
        print("❌ 模型为空，跳过组件测试")
        return False
    
    try:
        print("🔍 模型组件分析:")
        
        # 分析各组件参数
        components = {
            'conv2d': model.conv2d,
            'conv1d': model.conv1d,
            'temporal_model': model.temporal_model,
            'classifier': model.classifier
        }
        
        total_params = 0
        for name, component in components.items():
            if component is not None:
                params = count_parameters(component)
                total_params += params
                print(f"   {name}: {params:,} 参数")
        
        print(f"📊 总参数数量: {total_params:,}")
        
        # 测试各组件的输出
        batch_size = 2
        seq_len = 8
        videos = torch.randn(batch_size, 3, seq_len, 224, 224)
        
        model.eval()
        with torch.no_grad():
            # 3D CNN输出
            cnn_out = model.conv2d(videos)
            print(f"🔧 3D CNN输出形状: {cnn_out.shape}")
            
            # Transformer输出
            trans_in = cnn_out.permute(0, 2, 1)  # [B, T, C]
            trans_out = model.temporal_model(cnn_out)
            print(f"🔧 Transformer输出形状: {trans_out.shape}")
        
        print("✅ 模型组件测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 模型组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gpu_compatibility():
    """测试GPU兼容性"""
    print("\n" + "=" * 60)
    print("测试5: GPU兼容性")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("⚠️  CUDA不可用，跳过GPU测试")
        return True
    
    try:
        # 创建模型并移到GPU
        student_config = {
            'num_classes': 1086,
            'c2d_type': 'student',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 512,
            'weight_norm': True,
            'share_classifier': True,
            'loss_weights': {'SeqCTC': 1.0}
        }
        
        model = StudentSLRModel(**student_config)
        model = model.cuda()
        
        # 创建GPU数据
        videos = torch.randn(1, 3, 8, 224, 224).cuda()
        video_lengths = torch.tensor([8]).cuda()
        
        # GPU前向传播
        model.eval()
        with torch.no_grad():
            outputs = model(videos, video_lengths)
        
        print("✅ GPU兼容性测试通过")
        print(f"🎮 GPU设备: {torch.cuda.get_device_name()}")
        print(f"💾 GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始学生模型测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模型创建
    student_model = test_model_creation()
    test_results.append(student_model is not None)
    
    # 2. 测试前向传播
    forward_success = test_forward_pass(student_model)
    test_results.append(forward_success)
    
    # 3. 测试训练模式
    training_success = test_training_mode(student_model)
    test_results.append(training_success)
    
    # 4. 测试模型组件
    components_success = test_model_components(student_model)
    test_results.append(components_success)
    
    # 5. 测试GPU兼容性
    gpu_success = test_gpu_compatibility()
    test_results.append(gpu_success)
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    test_names = [
        "模型创建", "前向传播", "训练模式", "模型组件", "GPU兼容性"
    ]
    
    passed = sum(test_results)
    total = len(test_results)
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！学生模型设计成功！")
        print("\n📝 下一步建议:")
        print("   1. 运行知识蒸馏训练")
        print("   2. 调整超参数优化性能")
        print("   3. 添加更多蒸馏策略")
    else:
        print("⚠️  部分测试失败，请检查模型实现")
    
    return passed == total


if __name__ == "__main__":
    main()
