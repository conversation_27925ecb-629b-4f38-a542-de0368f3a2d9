import torch
import numpy as np
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR


class CustomCosineAnnealingLR(CosineAnnealingLR):
    def step(self, epoch=None):
        # 正确处理可选的 epoch 参数
        if epoch is None:
            if not self._step_count >= self.T_max:
                super().step()
        else:
            if not epoch >= self.T_max:
                super().step(epoch)


class Optimizer(object):
    def __init__(self, model, optim_dict):
        self.optim_dict = optim_dict

        # 创建优化器部分保持不变
        if self.optim_dict["optimizer"] == 'SGD':
            self.optimizer = optim.SGD(
                model.parameters(),
                lr=self.optim_dict['base_lr'],
                momentum=0.9,
                nesterov=self.optim_dict['nesterov'],
                weight_decay=self.optim_dict['weight_decay']
            )
        elif self.optim_dict["optimizer"] == 'AdamW':
            self.optimizer = optim.AdamW(
                model.parameters(),
                lr=self.optim_dict['base_lr'],
                weight_decay=self.optim_dict['weight_decay']
            )
        else:
            raise ValueError()

        self.scheduler = self.define_lr_scheduler(self.optimizer)

    def define_lr_scheduler(self, optimizer):
        """
        创建一个包含预热和自定义余弦退火的学习率调度器
        """
        total_epochs = 35
        warmup_epochs = 3

        # 创建预热调度器
        warmup_scheduler = LinearLR(
            optimizer,
            start_factor=0.1,
            end_factor=1.0,
            total_iters=warmup_epochs
        )

        # 使用自定义余弦退火调度器
        cosine_scheduler = CustomCosineAnnealingLR(
            optimizer,
            T_max=total_epochs - warmup_epochs,
            eta_min=1e-6
        )

        # 将两个调度器序列化
        scheduler = SequentialLR(
            optimizer,
            schedulers=[warmup_scheduler, cosine_scheduler],
            milestones=[warmup_epochs]
        )

        return scheduler

    # 其他方法保持不变
    def zero_grad(self):
        self.optimizer.zero_grad()

    def step(self):
        self.optimizer.step()

    def state_dict(self):
        return self.optimizer.state_dict()

    def load_state_dict(self, state_dict):
        self.optimizer.load_state_dict(state_dict)

    def to(self, device):
        for state in self.optimizer.state.values():
            for k, v in state.items():
                if isinstance(v, torch.Tensor):
                    state[k] = v.to(device)