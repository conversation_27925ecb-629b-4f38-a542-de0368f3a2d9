#!/usr/bin/env python3
"""
验证集和测试集测试脚本
快速测试数据加载和模型推理是否正常，无需完整训练
"""

import os
import sys
import yaml
import torch
import torch.optim as optim
import numpy as np
import argparse
from advanced_distillation_trainer import AdvancedDistillationTrainer
from dataset.dataloader_video import BaseFeeder


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loader(config, dataset_info, gloss_dict, mode='train'):
    """创建数据加载器"""
    feeder_args = config['feeder_args'].copy()
    
    # 添加必需的参数
    feeder_args['prefix'] = dataset_info['dataset_root']
    feeder_args['mode'] = mode.split("_")[0]  # 处理train_eval这样的模式
    feeder_args['transform_mode'] = (mode == 'train')
    feeder_args['dataset'] = config['dataset']
    
    # 动态导入feeder类
    feeder_class = config['feeder']
    module_name, class_name = feeder_class.rsplit('.', 1)
    module = __import__(module_name, fromlist=[class_name])
    feeder_cls = getattr(module, class_name)
    
    # 创建数据集 - 传入所有必需参数
    dataset = feeder_cls(
        gloss_dict=gloss_dict,
        kernel_size=['K5', 'K5'],  # 默认kernel size，会在模型加载后更新
        **feeder_args
    )
    
    # 创建数据加载器
    batch_size = config['batch_size'] if mode == 'train' else config['test_batch_size']
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=config['num_worker'],
        drop_last=(mode == 'train'),
        pin_memory=True,
        collate_fn=feeder_cls.collate_fn if hasattr(feeder_cls, 'collate_fn') else None
    )
    
    return data_loader, dataset


def test_data_loader(data_loader, mode_name, max_batches=10):
    """测试数据加载器"""
    print(f"\n{'='*60}")
    print(f"测试 {mode_name} 数据加载器")
    print(f"{'='*60}")
    
    successful_batches = 0
    failed_batches = 0
    
    for batch_idx, batch_data in enumerate(data_loader):
        if batch_idx >= max_batches:
            break
            
        try:
            # 解析数据
            if len(batch_data) == 5:
                videos, video_lengths, labels, label_lengths, info = batch_data
            else:
                print(f"❌ 批次 {batch_idx}: 数据格式不正确，期望5个元素，得到{len(batch_data)}个")
                failed_batches += 1
                continue
            
            # 数据验证
            if videos is None or labels is None or label_lengths is None or video_lengths is None:
                print(f"❌ 批次 {batch_idx}: 数据不完整")
                failed_batches += 1
                continue
            
            # 检查是否有空标签
            if len(labels) == 0 or len(label_lengths) == 0:
                print(f"❌ 批次 {batch_idx}: 标签为空")
                failed_batches += 1
                continue
            
            # 打印详细信息
            print(f"✅ 批次 {batch_idx}:")
            print(f"   videos.shape: {videos.shape}")
            print(f"   video_lengths: {video_lengths} (type: {type(video_lengths)})")
            print(f"   labels: {labels[:10]}... (len: {len(labels)}, type: {type(labels)})")
            print(f"   label_lengths: {label_lengths} (type: {type(label_lengths)})")
            
            if isinstance(label_lengths, torch.Tensor):
                print(f"   label_lengths.shape: {label_lengths.shape}")
                print(f"   label_lengths.dim(): {label_lengths.dim()}")
            
            # 检查序列长度
            max_video_len = videos.size(1)
            
            # 安全地获取最大标签长度
            if isinstance(label_lengths, torch.Tensor):
                if label_lengths.dim() > 0:
                    max_label_len = label_lengths.max().item()
                else:
                    max_label_len = label_lengths.item()
            else:
                max_label_len = max(label_lengths) if hasattr(label_lengths, '__iter__') else label_lengths
            
            print(f"   max_video_len: {max_video_len}, max_label_len: {max_label_len}")
            
            if max_video_len > 1000 or max_label_len > 100:
                print(f"⚠️  批次 {batch_idx}: 序列过长 video={max_video_len}, label={max_label_len}")
            
            successful_batches += 1
            
        except Exception as e:
            print(f"❌ 批次 {batch_idx} 出错: {e}")
            import traceback
            traceback.print_exc()
            failed_batches += 1
    
    print(f"\n📊 {mode_name} 数据加载器测试结果:")
    print(f"   成功批次: {successful_batches}")
    print(f"   失败批次: {failed_batches}")
    print(f"   成功率: {successful_batches/(successful_batches+failed_batches)*100:.1f}%")
    
    return successful_batches > 0 and failed_batches == 0


def test_model_inference(trainer, data_loader, mode_name, device, max_batches=5):
    """测试模型推理"""
    print(f"\n{'='*60}")
    print(f"测试 {mode_name} 模型推理")
    print(f"{'='*60}")
    
    successful_inferences = 0
    failed_inferences = 0
    
    for batch_idx, batch_data in enumerate(data_loader):
        if batch_idx >= max_batches:
            break
            
        try:
            # 解析数据
            if len(batch_data) == 5:
                videos, video_lengths, labels, label_lengths, info = batch_data
            else:
                print(f"❌ 推理批次 {batch_idx}: 数据格式不正确")
                failed_inferences += 1
                continue
            
            # 移动到设备
            videos = videos.to(device)
            labels = labels.to(device)
            label_lengths = label_lengths.to(device)
            video_lengths = video_lengths.to(device)
            
            # 模型推理
            with torch.no_grad():
                loss_dict, student_out, teacher_out = trainer.train_step(
                    (videos, labels, label_lengths, video_lengths)
                )
            
            print(f"✅ 推理批次 {batch_idx}:")
            print(f"   总损失: {loss_dict['total_loss']:.4f}")
            print(f"   硬标签损失: {loss_dict['hard_loss']:.4f}")
            print(f"   软标签损失: {loss_dict['soft_loss']:.4f}")
            print(f"   特征损失: {loss_dict['feature_loss']:.4f}")
            
            successful_inferences += 1
            
        except Exception as e:
            print(f"❌ 推理批次 {batch_idx} 出错: {e}")
            import traceback
            traceback.print_exc()
            failed_inferences += 1
    
    print(f"\n📊 {mode_name} 模型推理测试结果:")
    print(f"   成功推理: {successful_inferences}")
    print(f"   失败推理: {failed_inferences}")
    print(f"   成功率: {successful_inferences/(successful_inferences+failed_inferences)*100:.1f}%")
    
    return successful_inferences > 0 and failed_inferences == 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='验证集和测试集测试脚本')
    parser.add_argument('--config', default='configs/student_distillation.yaml', help='配置文件路径')
    parser.add_argument('--teacher_model', default='dev_19.48_epoch35_model.pt', help='教师模型路径')
    parser.add_argument('--device', default='0', help='GPU设备')
    parser.add_argument('--max_batches', type=int, default=10, help='测试的最大批次数')
    parser.add_argument('--test_inference', action='store_true', help='是否测试模型推理')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 加载数据集信息
    dataset_config_path = f"./configs/{config['dataset']}.yaml"
    with open(dataset_config_path, 'r') as f:
        dataset_info = yaml.safe_load(f)
    
    # 加载词典
    gloss_dict = np.load(dataset_info['dict_path'], allow_pickle=True).item()
    print(f"加载词典: {len(gloss_dict)} 个词汇")
    
    # 设置设备
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    print(f"数据集: {config['dataset']}")
    print(f"类别数: {len(gloss_dict) + 1}")
    
    # 测试数据加载器
    print(f"\n🚀 开始测试数据加载器...")
    
    # 1. 测试训练集
    try:
        train_loader, train_dataset = create_data_loader(config, dataset_info, gloss_dict, 'train')
        print(f"训练集大小: {len(train_dataset)}")
        train_success = test_data_loader(train_loader, "训练集", args.max_batches)
    except Exception as e:
        print(f"❌ 训练集加载失败: {e}")
        train_success = False
    
    # 2. 测试验证集
    try:
        val_loader, val_dataset = create_data_loader(config, dataset_info, gloss_dict, 'dev')
        print(f"验证集大小: {len(val_dataset)}")
        val_success = test_data_loader(val_loader, "验证集", args.max_batches)
    except Exception as e:
        print(f"❌ 验证集加载失败: {e}")
        val_success = False
    
    # 3. 测试测试集
    try:
        test_loader, test_dataset = create_data_loader(config, dataset_info, gloss_dict, 'test')
        print(f"测试集大小: {len(test_dataset)}")
        test_success = test_data_loader(test_loader, "测试集", args.max_batches)
    except Exception as e:
        print(f"❌ 测试集加载失败: {e}")
        test_success = False
    
    # 4. 测试模型推理（可选）
    if args.test_inference:
        print(f"\n🚀 开始测试模型推理...")
        
        try:
            # 学生模型配置
            student_config = {
                'num_classes': len(gloss_dict) + 1,
                'c2d_type': 'student',
                'conv_type': config['model_args']['conv_type'],
                'use_bn': config['model_args']['use_bn'],
                'hidden_size': config['model_args'].get('hidden_size', 512),
                'weight_norm': config['model_args'].get('weight_norm', True),
                'share_classifier': config['model_args'].get('share_classifier', True),
                'loss_weights': config['loss_weights']
            }
            
            # 创建训练器
            trainer = AdvancedDistillationTrainer(
                teacher_model_path=args.teacher_model,
                student_model_config=student_config,
                device=device,
                alpha=0.7,
                temperature=3.0,
                feature_weight=0.001
            )
            
            # 测试推理
            if train_success:
                train_inference_success = test_model_inference(trainer, train_loader, "训练集", device, 3)
            if val_success:
                val_inference_success = test_model_inference(trainer, val_loader, "验证集", device, 3)
            if test_success:
                test_inference_success = test_model_inference(trainer, test_loader, "测试集", device, 3)
                
        except Exception as e:
            print(f"❌ 模型推理测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 总结
    print(f"\n{'='*60}")
    print(f"📋 测试总结")
    print(f"{'='*60}")
    print(f"训练集数据加载: {'✅ 成功' if train_success else '❌ 失败'}")
    print(f"验证集数据加载: {'✅ 成功' if val_success else '❌ 失败'}")
    print(f"测试集数据加载: {'✅ 成功' if test_success else '❌ 失败'}")
    
    if train_success and val_success and test_success:
        print(f"\n🎉 所有数据集测试通过！可以开始正式训练。")
        return True
    else:
        print(f"\n⚠️  部分数据集测试失败，请检查配置和数据。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
