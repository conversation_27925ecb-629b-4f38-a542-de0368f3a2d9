# 学生模型知识蒸馏训练配置
feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014-T
num_epoch: 40
work_dir: ./work_dir/student_distillation/
batch_size: 1  # 进一步减少batch size以避免GPU内存不足
random_seed: 726
test_batch_size: 1
num_worker: 4
device: 0
log_interval: 1000
eval_interval: 1
save_interval: 5
evaluate_tool: python

# 损失权重配置（降低蒸馏损失权重以提高稳定性）
loss_weights:
  SeqCTC: 1.0      # 序列CTC损失
  ConvCTC: 1.0     # 卷积CTC损失
  Dist: 5.0        # 降低知识蒸馏损失权重，避免训练不稳定

# 优化器配置（降低学习率以避免梯度爆炸）
optimizer_args:
  optimizer: AdamW
  base_lr: 0.0002   # 降低学习率，避免训练不稳定
  step: [8, 15, 21, 27, 35]  # 更频繁的学习率衰减
  learning_ratio: 1
  weight_decay: 0.0001  # 减少权重衰减
  start_epoch: 0
  nesterov: False

# 数据加载配置
feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0
  input_size: 224

# 学生模型配置
model: student_slr_network.StudentSLRModel
decode_mode: beam

model_args:
  num_classes: 1116  # 将在运行时根据词典自动设置
  c2d_type: 'student'  # 标识使用学生模型
  conv_type: 2
  use_bn: 1
  hidden_size: 512
  share_classifier: True
  weight_norm: True
  # 知识蒸馏特定参数
  teacher_model_path: './dev_19.48_epoch35_model.pt'  # 教师模型路径
  distillation_alpha: 0.7      # 增加硬标签权重，提高训练稳定性
  distillation_temperature: 6.0  # 增加蒸馏温度，使软标签更平滑

# 可选：忽略某些权重层（如果需要）
ignore_weights: []

# 可选：从检查点恢复训练
# load_checkpoints: './work_dir/student_distillation/checkpoint.pt'

# 可选：加载预训练权重
# load_weights: './pretrained_student.pt'
