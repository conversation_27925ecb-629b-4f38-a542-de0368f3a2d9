# 学生模型知识蒸馏训练配置 - 使用真实数据集
feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014-T
num_epoch: 100  # 🔥 大幅增加训练轮数，让模型充分学习
work_dir: ./work_dir/student_distillation/
batch_size: 2  # 增加batch size提高训练效率
random_seed: 726
test_batch_size: 2
num_worker: 8
device: 0
log_interval: 50  # 更频繁的日志输出
eval_interval: 1  # 每个epoch都验证
save_interval: 5  # 每5个epoch保存一次
evaluate_tool: python

# 损失权重配置 - 知识蒸馏专用
loss_weights:
  SeqCTC: 1.0      # 序列CTC损失
  ConvCTC: 0.5     # 卷积CTC损失（降低权重）
  Dist: 0.0        # 在蒸馏训练器中单独处理

# 优化器配置 - 激进优化知识蒸馏
optimizer_args:
  optimizer: AdamW
  base_lr: 0.01     # 🔥 激进提高学习率 2倍
  step: [20, 35, 45]  # 延后学习率衰减，让模型充分学习
  learning_ratio: 1
  weight_decay: 0.0005  # 减少权重衰减，避免过度正则化
  start_epoch: 0
  nesterov: False
  # 新增激进优化参数
  betas: [0.9, 0.999]  # Adam的beta参数
  eps: 0.00000001  # 1e-8 用小数形式避免YAML解析问题
  amsgrad: false

# 数据加载配置
feeder_args:
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0
  input_size: 224
  # 其他参数会在运行时自动添加

# 学生模型配置
model: student_slr_network.StudentSLRModel
decode_mode: beam

model_args:
  num_classes: 1116  # 匹配教师模型
  c2d_type: 'student'
  conv_type: 2
  use_bn: 1
  hidden_size: 512  # 学生模型隐藏维度
  share_classifier: True
  weight_norm: True

# 知识蒸馏特定配置 - 激进优化
distillation:
  teacher_model_path: './dev_19.48_epoch35_model.pt'
  alpha: 0.6              # 🔥 平衡硬软标签，让学生模型更多学习教师知识
  temperature: 4.0        # 🔥 提高温度，让软标签更平滑，包含更多知识
  feature_weight: 0.001   # 🔥 适度增加特征蒸馏权重，学习中间表示

# 可选：从检查点恢复训练
# load_checkpoints: './work_dir/student_distillation/checkpoint.pt'
