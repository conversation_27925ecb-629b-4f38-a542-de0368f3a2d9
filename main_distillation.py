#!/usr/bin/env python3
"""
知识蒸馏主训练脚本 - 使用真实数据集
类似于原始的main.py，但集成了知识蒸馏功能
"""

import os
import sys
import yaml
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import MultiStepLR
import numpy as np
import argparse
from advanced_distillation_trainer import AdvancedDistillationTrainer
from dataset.dataloader_video import BaseFeeder
import utils
from evaluation.slr_eval.wer_calculation import evaluate


def write2file(path, info, output):
    """将预测结果写入CTM文件 - 与seq_scripts.py中的标准函数一致"""
    filereader = open(path, "w")
    for sample_idx, sample in enumerate(output):
        for word_idx, word in enumerate(sample):
            filereader.writelines(
                "{} 1 {:.2f} {:.2f} {}\n".format(info[sample_idx],
                                                 word_idx * 1.0 / 100,
                                                 (word_idx + 1) * 1.0 / 100,
                                                 word[0]))
    filereader.close()


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loader(config, dataset_info, gloss_dict, mode='train'):
    """创建数据加载器"""
    feeder_args = config['feeder_args'].copy()

    # 添加必需的参数
    feeder_args['prefix'] = dataset_info['dataset_root']
    feeder_args['mode'] = mode.split("_")[0]  # 处理train_eval这样的模式
    feeder_args['transform_mode'] = (mode == 'train')
    feeder_args['dataset'] = config['dataset']

    # 动态导入feeder类
    feeder_class = config['feeder']
    module_name, class_name = feeder_class.rsplit('.', 1)
    module = __import__(module_name, fromlist=[class_name])
    feeder_cls = getattr(module, class_name)

    # 创建数据集 - 传入所有必需参数
    dataset = feeder_cls(
        gloss_dict=gloss_dict,
        kernel_size=['K5', 'K5'],  # 默认kernel size，会在模型加载后更新
        **feeder_args
    )

    # 创建数据加载器
    batch_size = config['batch_size'] if mode == 'train' else config['test_batch_size']
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=config['num_worker'],
        drop_last=(mode == 'train'),
        pin_memory=True,
        collate_fn=feeder_cls.collate_fn if hasattr(feeder_cls, 'collate_fn') else None
    )

    return data_loader, dataset


def train_epoch(trainer, data_loader, optimizer, epoch, device, log_interval=100):
    """训练一个epoch"""
    trainer.student_model.train()
    total_loss = 0
    total_hard_loss = 0
    total_soft_loss = 0
    total_feature_loss = 0
    num_batches = 0
    
    for batch_idx, batch_data in enumerate(data_loader):
        try:
            # 解析数据 - 基于原始数据加载器格式
            # 数据格式: (videos, video_lengths, labels, label_lengths, info)
            if len(batch_data) == 5:
                videos, video_lengths, labels, label_lengths, info = batch_data
            else:
                print(f"跳过批次 {batch_idx}: 数据格式不正确，期望5个元素，得到{len(batch_data)}个")
                continue

            # 数据验证
            if videos is None or labels is None or label_lengths is None or video_lengths is None:
                print(f"跳过批次 {batch_idx}: 数据不完整")
                continue

            # 检查是否有空标签
            if len(labels) == 0 or len(label_lengths) == 0:
                print(f"跳过批次 {batch_idx}: 标签为空")
                continue

            # 检查序列长度是否合理
            max_video_len = videos.size(1)

            # 安全地获取最大标签长度
            if isinstance(label_lengths, torch.Tensor):
                if label_lengths.dim() > 0:
                    max_label_len = label_lengths.max().item()
                else:
                    max_label_len = label_lengths.item()  # 标量张量
            else:
                max_label_len = max(label_lengths) if hasattr(label_lengths, '__iter__') else label_lengths

            if max_video_len > 1000 or max_label_len > 100:
                print(f"跳过批次 {batch_idx}: 序列过长 video={max_video_len}, label={max_label_len}")
                continue

            # 移动到设备
            videos = videos.to(device)
            labels = labels.to(device)
            label_lengths = label_lengths.to(device)
            video_lengths = video_lengths.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            
            loss_dict, student_out, teacher_out = trainer.train_step(
                (videos, labels, label_lengths, video_lengths)
            )
            
            # 反向传播
            loss_dict['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(trainer.student_model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 累计损失
            total_loss += loss_dict['total_loss'].item()
            total_hard_loss += loss_dict['hard_loss'].item()
            total_soft_loss += loss_dict['soft_loss'].item()
            total_feature_loss += loss_dict['feature_loss'].item()
            num_batches += 1
            
            # 打印日志
            if batch_idx % log_interval == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}/{len(data_loader)}: '
                      f'Loss={loss_dict["total_loss"].item():.4f}, '
                      f'Hard={loss_dict["hard_loss"].item():.4f}, '
                      f'Soft={loss_dict["soft_loss"].item():.4f}, '
                      f'Feature={loss_dict["feature_loss"].item():.4f}')
                
        except Exception as e:
            print(f"训练批次 {batch_idx} 出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 返回平均损失
    if num_batches > 0:
        return {
            'total_loss': total_loss / num_batches,
            'hard_loss': total_hard_loss / num_batches,
            'soft_loss': total_soft_loss / num_batches,
            'feature_loss': total_feature_loss / num_batches
        }
    else:
        return {'total_loss': float('inf'), 'hard_loss': 0, 'soft_loss': 0, 'feature_loss': 0}


def validate_epoch(trainer, data_loader, device, dataset, config, work_dir="./work_dir/", mode="dev"):
    """验证一个epoch并计算识别准确率 - 完全按照seq_eval的标准方法"""
    # 确保模型处于评估状态
    trainer.student_model.eval()
    trainer.teacher_model.eval()

    total_loss = 0
    num_batches = 0

    # 按照seq_eval的标准方法收集数据
    total_sent = []      # 学生模型预测结果
    total_conv_sent = [] # 学生模型conv预测结果
    total_info = []      # 文件信息

    # 创建device对象（模拟seq_eval中的device.data_to_device）
    import utils
    device_obj = utils.GpuDataParallel()
    device_obj.set_device(device)
    
    with torch.no_grad():
        for batch_idx, data in enumerate(data_loader):
            try:
                # 完全按照seq_eval的方法处理数据
                vid = device_obj.data_to_device(data[0])
                vid_lgt = device_obj.data_to_device(data[1])
                label = device_obj.data_to_device(data[2])
                label_lgt = device_obj.data_to_device(data[3])

                # 添加调试信息
                if batch_idx < 3:
                    print(f"验证批次 {batch_idx} 调试信息:")
                    print(f"  原始vid.shape: {vid.shape}")
                    print(f"  vid_lgt: {vid_lgt}")
                    print(f"  label长度: {len(label)}")
                    print(f"  label_lgt: {label_lgt}")

                # 关键修复：学生模型需要 [B, C, T, H, W] 格式
                if len(vid.shape) == 5:
                    # 从 [B, T, C, H, W] 转换为 [B, C, T, H, W]
                    vid_student = vid.permute(0, 2, 1, 3, 4)
                    if batch_idx < 3:
                        print(f"  转换后vid_student.shape: {vid_student.shape}")
                else:
                    vid_student = vid
                    if batch_idx < 3:
                        print(f"  vid不是5维，直接使用: {vid_student.shape}")

                # 学生模型推理 - 使用正确的数据格式
                ret_dict = trainer.student_model(vid_student, vid_lgt, label=label, label_lgt=label_lgt)

                if batch_idx < 3:
                    print(f"  模型推理成功")
                    print(f"  recognized_sents数量: {len(ret_dict['recognized_sents']) if ret_dict['recognized_sents'] else 0}")
                    print(f"  conv_sents数量: {len(ret_dict['conv_sents']) if ret_dict['conv_sents'] else 0}")

                # 收集预测结果 - 完全按照seq_eval的方法
                total_info += [file_name.split("|")[0] for file_name in data[-1]]
                total_sent += ret_dict['recognized_sents']
                total_conv_sent += ret_dict['conv_sents']

                # 计算损失（可选，用于监控）
                if hasattr(trainer.student_model, 'criterion_calculation'):
                    loss = trainer.student_model.criterion_calculation(ret_dict, label, label_lgt)
                    total_loss += loss.item()
                    num_batches += 1

            except Exception as e:
                print(f"验证批次 {batch_idx} 出错: {e}")
                continue
    
    # 计算平均损失
    avg_loss = total_loss / num_batches if num_batches > 0 else float('inf')

    # 按照seq_eval的标准方法计算WER
    student_wer = None

    try:
        print(f"收集到 {len(total_sent)} 个学生模型预测")
        print(f"收集到 {len(total_conv_sent)} 个学生模型conv预测")
        print(f"收集到 {len(total_info)} 个文件信息")

        # 确保work_dir存在
        os.makedirs(work_dir, exist_ok=True)

        # 完全按照seq_eval的方法写入文件和计算WER
        python_eval = True
        write2file(work_dir + "output-hypothesis-{}.ctm".format(mode), total_info, total_sent)
        write2file(work_dir + "output-hypothesis-{}-conv.ctm".format(mode), total_info, total_conv_sent)

        # 计算学生模型WER - 完全按照seq_eval的方法
        conv_ret = evaluate(
            prefix=work_dir, mode=mode, output_file="output-hypothesis-{}-conv.ctm".format(mode),
            evaluate_dir=config['dataset_info']['evaluation_dir'],
            evaluate_prefix=config['dataset_info']['evaluation_prefix'],
            output_dir="epoch_validation_result/",
            python_evaluate=python_eval,
        )
        student_wer = evaluate(
            prefix=work_dir, mode=mode, output_file="output-hypothesis-{}.ctm".format(mode),
            evaluate_dir=config['dataset_info']['evaluation_dir'],
            evaluate_prefix=config['dataset_info']['evaluation_prefix'],
            output_dir="epoch_validation_result/",
            python_evaluate=python_eval,
            triplet=True,
        )

        print(f"学生模型WER: {student_wer:.2f}%")
        print(f"学生模型Conv WER: {conv_ret:.2f}%")

    except Exception as e:
        print(f"WER评估计算出错: {e}")
        import traceback
        traceback.print_exc()
        student_wer = 100.0

    return {
        'avg_loss': avg_loss,
        'student_wer': student_wer,
        'teacher_wer': None,
        'num_samples': len(total_sent)
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='知识蒸馏训练 - 使用真实数据集')
    parser.add_argument('--config', default='configs/student_distillation.yaml', help='配置文件路径')
    parser.add_argument('--teacher_model', default='dev_19.48_epoch35_model.pt', help='教师模型路径')
    parser.add_argument('--device', default='0', help='GPU设备')
    parser.add_argument('--alpha', type=float, default=0.5, help='硬标签权重')
    parser.add_argument('--temperature', type=float, default=4.0, help='蒸馏温度')
    parser.add_argument('--feature_weight', type=float, default=0.001, help='特征蒸馏权重')

    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 加载数据集信息
    dataset_config_path = f"./configs/{config['dataset']}.yaml"
    with open(dataset_config_path, 'r') as f:
        dataset_info = yaml.safe_load(f)

    # 将数据集信息添加到config中，供验证函数使用
    config['dataset_info'] = dataset_info

    # 加载词典
    gloss_dict = np.load(dataset_info['dict_path'], allow_pickle=True).item()
    print(f"加载词典: {len(gloss_dict)} 个词汇")

    # 设置设备
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 学生模型配置 - 使用实际的类别数
    student_config = {
        'num_classes': len(gloss_dict) + 1,  # +1 for blank token
        'c2d_type': 'student',
        'conv_type': config['model_args']['conv_type'],
        'use_bn': config['model_args']['use_bn'],
        'hidden_size': config['model_args'].get('hidden_size', 512),
        'weight_norm': config['model_args'].get('weight_norm', True),
        'share_classifier': config['model_args'].get('share_classifier', True),
        'loss_weights': config['loss_weights']
    }

    print(f"数据集: {config['dataset']}")
    print(f"类别数: {student_config['num_classes']}")
    
    # 创建训练器
    print("创建知识蒸馏训练器...")
    trainer = AdvancedDistillationTrainer(
        teacher_model_path=args.teacher_model,
        student_model_config=student_config,
        device=device,
        alpha=args.alpha,
        temperature=args.temperature,
        feature_weight=args.feature_weight
    )
    
    # 创建数据加载器
    print("创建数据加载器...")
    train_loader, train_dataset = create_data_loader(config, dataset_info, gloss_dict, 'train')
    val_loader, val_dataset = create_data_loader(config, dataset_info, gloss_dict, 'dev')  # 使用dev作为验证集

    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    # 创建优化器
    optimizer = optim.AdamW(
        trainer.student_model.parameters(),
        lr=config['optimizer_args']['base_lr'],
        weight_decay=config['optimizer_args']['weight_decay']
    )
    
    # 学习率调度器
    scheduler = MultiStepLR(
        optimizer, 
        milestones=config['optimizer_args']['step'], 
        gamma=0.1
    )
    
    # 开始训练
    num_epochs = config['num_epoch']
    print(f"开始训练 {num_epochs} 个epoch...")
    
    best_loss = float('inf')
    best_wer = float('inf')
    
    for epoch in range(num_epochs):
        print(f"\n=== Epoch {epoch+1}/{num_epochs} ===")
        
        # 训练
        train_losses = train_epoch(trainer, train_loader, optimizer, epoch+1, device, 
                                 config.get('log_interval', 100))
        
        print(f'训练损失: Total={train_losses["total_loss"]:.4f}, '
              f'Hard={train_losses["hard_loss"]:.4f}, '
              f'Soft={train_losses["soft_loss"]:.4f}, '
              f'Feature={train_losses["feature_loss"]:.4f}')
        
        # 验证
        if (epoch + 1) % config.get('eval_interval', 1) == 0:
            print("开始验证...")
            # 创建工作目录
            work_dir = f"./work_dir/distillation_epoch_{epoch+1}/"
            val_results = validate_epoch(trainer, val_loader, device, val_dataset,
                                       config, work_dir, mode="dev")

            print(f'验证损失: {val_results["avg_loss"]:.4f}')
            if val_results["student_wer"] is not None:
                print(f'学生模型WER: {val_results["student_wer"]:.2f}%')
            if val_results["teacher_wer"] is not None:
                print(f'教师模型WER: {val_results["teacher_wer"]:.2f}%')
        
        # 更新学习率
        scheduler.step()
        
        # 保存模型
        if (epoch + 1) % config.get('save_interval', 5) == 0:
            save_path = os.path.join(config['work_dir'], f'student_epoch_{epoch+1}.pt')
            os.makedirs(config['work_dir'], exist_ok=True)
            trainer.save_student_model(save_path, epoch+1, optimizer.state_dict(), {
                'train_loss': train_losses["total_loss"],
                'val_loss': val_results["avg_loss"] if 'val_results' in locals() else None
            })
            
            # 保存最佳模型
            if train_losses["total_loss"] < best_loss:
                best_loss = train_losses["total_loss"]
                best_path = os.path.join(config['work_dir'], 'best_student_model.pt')
                trainer.save_student_model(best_path, epoch+1, optimizer.state_dict())
                print(f'保存最佳模型: {best_path}')
    
    print(f"\n训练完成！最佳损失: {best_loss:.4f}")


if __name__ == "__main__":
    main()
