[ Tue Jul 22 19:46:30 2025 ] Parameters:
{'work_dir': './work_dir/baseline_res18/', 'config': '/root/lanyun-tmp/CorrNet-main/configs/baseline.yaml', 'random_fix': True, 'device': '0', 'phase': 'train', 'save_interval': 5, 'random_seed': 726, 'eval_interval': 1, 'print_log': True, 'log_interval': 10000, 'evaluate_tool': 'python', 'feeder': 'dataset.dataloader_video.BaseFeeder', 'dataset': 'phoenix2014-T', 'dataset_info': {'dataset_root': './dataset/phoenix2014-T', 'dict_path': './preprocess/phoenix2014-T/gloss_dict.npy', 'evaluation_dir': './evaluation/slr_eval', 'evaluation_prefix': 'phoenix2014-T-groundtruth'}, 'num_worker': 10, 'feeder_args': {'mode': 'test', 'datatype': 'video', 'num_gloss': -1, 'drop_ratio': 1.0, 'frame_interval': 1, 'image_scale': 1.0, 'input_size': 224, 'prefix': './dataset/phoenix2014-T', 'transform_mode': False}, 'model': 'slr_network.SLRModel', 'model_args': {'num_classes': 1116, 'c2d_type': 'resnet18', 'conv_type': 2, 'use_bn': 1, 'share_classifier': True, 'weight_norm': True}, 'load_weights': None, 'load_checkpoints': None, 'decode_mode': 'beam', 'ignore_weights': [], 'batch_size': 2, 'test_batch_size': 2, 'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 25.0}, 'optimizer_args': {'optimizer': 'AdamW', 'base_lr': 0.001, 'step': [8, 15, 22, 28, 33, 38, 42, 45], 'learning_ratio': 1, 'weight_decay': 0.001, 'start_epoch': 0, 'nesterov': False}, 'num_epoch': 80}

[ Tue Jul 22 19:46:44 2025 ] 	Epoch: 0, Batch(0/3548) done. Loss: 388.18286133  lr:0.000100
