import pdb
import copy
import utils
import torch
import types
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from modules.criterions import SeqKD
from modules import BiLSTMLayer, TemporalConv
import modules.res124 as resnet
from typing import Optional, Tuple
import torch
import torch.utils.checkpoint
from torch import nn
import random
from transformers.activations import ACT2FN
from transformers import SwinConfig

import math
from modules.trans2 import Encoder
from student_model import SimplifiedStudentModel

# 导入教师模型
import sys
import os
sys.path.append('12_26_19.48_cnn_trans_1d')
sys.path.append(os.path.join(os.path.dirname(__file__), '12_26_19.48_cnn_trans_1d'))

# 尝试导入教师模型，如果失败则创建一个占位符
try:
    from complete_model import CompleteModel
except ImportError:
    try:
        # 尝试从12_26_19.48_cnn_trans_1d目录导入
        import importlib.util
        spec = importlib.util.spec_from_file_location("complete_model", "12_26_19.48_cnn_trans_1d/complete_model.py")
        complete_model_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(complete_model_module)
        CompleteModel = complete_model_module.CompleteModel
    except Exception as e:
        print(f"Warning: Could not import CompleteModel: {e}")
        # 创建一个占位符类
        class CompleteModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.dummy = nn.Linear(1, 1116)
            def forward(self, x):
                return torch.randn(x.shape[0], 1116)

class StudentSLRModel(nn.Module):
    """
    集成到原框架的学生模型
    保持与原SLRModel相同的接口，便于无缝替换
    """
    def __init__(
            self, num_classes, c2d_type='student', conv_type=2, use_bn=False,
            hidden_size=512, gloss_dict=None, loss_weights=None,
            weight_norm=True, share_classifier=True, teacher_model_path=None,
            distillation_alpha=0.5, distillation_temperature=4.0
    ):
        super(StudentSLRModel, self).__init__()
        
        # 基本参数
        self.num_classes = num_classes
        self.loss_weights = loss_weights
        self.gloss_dict = gloss_dict
        self.hidden_size = hidden_size
        
        # 知识蒸馏参数
        self.distillation_alpha = distillation_alpha
        self.distillation_temperature = distillation_temperature
        self.teacher_model_path = teacher_model_path
        
        # 简化的学生模型核心
        from student_model import create_simplified_student_model
        self.student_backbone = create_simplified_student_model(
            num_classes=num_classes,
            hidden_dim=512  # 增加隐藏维度以提升表达能力
        )

        # 添加解码器（与原框架兼容）
        if gloss_dict is not None:
            import utils
            self.decoder = utils.Decode(gloss_dict, num_classes, 'beam')
        else:
            self.decoder = None
        
        # 为了兼容原框架，添加必要的组件
        # 简化模型没有conv1d，创建一个假的kernel_size属性
        class DummyConv1d:
            def __init__(self):
                self.kernel_size = ['K3', 'K3']  # 假的kernel_size用于兼容

        self.conv1d = DummyConv1d()
        
        # CTC相关组件（保持与原模型一致）
        self.classifier = nn.Linear(512, num_classes)
        self.conv_classifier = nn.Linear(512, num_classes)
        
        # 损失函数
        self.loss_fn = torch.nn.CTCLoss(reduction='none', zero_infinity=False)
        
        # 教师模型（训练时加载）
        self.teacher_model = None
        self.load_teacher_model()
        
    def load_teacher_model(self):
        """加载教师模型"""
        if self.teacher_model_path and self.training:
            try:
                print(f"Loading teacher model from {self.teacher_model_path}")
                self.teacher_model = CompleteModel()
                checkpoint = torch.load(self.teacher_model_path, map_location='cpu')
                
                if "model_state_dict" in checkpoint:
                    # 尝试加载权重，如果失败就使用随机初始化的教师模型
                    try:
                        self.teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
                        print("✅ Teacher model weights loaded successfully!")
                    except Exception as e:
                        print(f"⚠️ Teacher model weight loading failed: {e}")
                        print("Using randomly initialized teacher model for distillation")
                
                # 冻结教师模型
                self.teacher_model.eval()
                for param in self.teacher_model.parameters():
                    param.requires_grad = False

                # 确保教师模型在正确的设备上
                if hasattr(self, 'device'):
                    self.teacher_model = self.teacher_model.to(self.device)
                elif next(self.student_backbone.parameters()).is_cuda:
                    device = next(self.student_backbone.parameters()).device
                    self.teacher_model = self.teacher_model.to(device)
                    
            except Exception as e:
                print(f"❌ Failed to load teacher model: {e}")
                self.teacher_model = None
    
    def forward(self, vid, vid_lgt, label=None, label_lgt=None):
        """
        简化但正确的前向传播 - 确保时序对齐

        Args:
            vid: 视频数据 [B, T, C, H, W]
            vid_lgt: 视频长度 [B]
            label: 标签 (训练时使用)
            label_lgt: 标签长度 (训练时使用)
        """
        # 调试信息
        if not self.training:
            print(f"Input vid shape: {vid.shape}, vid_lgt: {vid_lgt}")

        # 调整输入格式: [B, T, C, H, W] -> [B, C, T, H, W]
        if len(vid.shape) == 5:
            vid_input = vid.permute(0, 2, 1, 3, 4)
        else:
            vid_input = vid

        # 验证序列长度约束
        T = vid_input.shape[2]
        if label is not None and label_lgt is not None:
            max_label_len = label_lgt.max().item()
            if T < max_label_len:
                print(f"WARNING: Output length {T} < max label length {max_label_len}")

        # 学生模型前向传播
        student_logits, student_features = self.student_backbone(vid_input, return_features=True)

        if not self.training:
            print(f"Student logits shape: {student_logits.shape}")

        # 解码（仅在评估时进行）
        if not self.training and self.decoder is not None:
            # 转换格式: [B, T, num_classes] -> [T, B, num_classes] 以匹配原框架
            logits_for_decode = student_logits.permute(1, 0, 2)
            pred = self.decoder.decode(logits_for_decode, vid_lgt, batch_first=False, probs=False)
            conv_pred = self.decoder.decode(logits_for_decode, vid_lgt, batch_first=False, probs=False)
        else:
            pred = None
            conv_pred = None

        # 构造返回字典 - 简化版本
        ret_dict = {
            'visual_feat': student_features['final_features'],  # [B, hidden_dim]
            'conv_logits': student_logits,                      # [B, T, num_classes]
            'sequence_logits': student_logits,                  # [B, T, num_classes]
            'conv_sents': conv_pred,
            'recognized_sents': pred,
            'feat_len': vid_lgt,
        }

        # 知识蒸馏（暂时禁用，先确保基础功能正常）
        # if self.training and self.teacher_model is not None and label is not None:
        #     with torch.no_grad():
        #         teacher_logits = self.teacher_model(vid_input)
        #         ret_dict['teacher_logits'] = teacher_logits

        return ret_dict
    
    def criterion_calculation(self, ret_dict, label, label_lgt):
        """
        简化的损失计算 - 专注于CTC训练
        """
        loss = 0

        # 调试信息
        if hasattr(self, '_debug_step'):
            self._debug_step += 1
        else:
            self._debug_step = 1

        if self._debug_step <= 5:  # 只打印前5个batch的调试信息
            print(f"Computing loss for logits shape: {ret_dict['sequence_logits'].shape}")
            print(f"Label shape: {label.shape}, Label lengths: {label_lgt}")

        # 主要CTC损失 - 使用sequence_logits
        if 'sequence_logits' in ret_dict:
            sequence_logits = ret_dict['sequence_logits']  # [B, T, num_classes]

            # 转换为CTC期望的格式: [B, T, C] -> [T, B, C]
            sequence_logits_ctc = sequence_logits.permute(1, 0, 2)  # [T, B, num_classes]

            # 输入序列长度 = 输出序列长度
            T, B = sequence_logits_ctc.shape[:2]
            input_lengths = torch.full((B,), T, dtype=torch.long, device=sequence_logits.device)

            # 验证CTC约束
            max_label_len = label_lgt.max().item() if len(label_lgt) > 0 else 0
            if T < max_label_len:
                if self._debug_step <= 5:
                    print(f"ERROR: CTC constraint violated! T={T} < max_label_len={max_label_len}")
                return torch.tensor(float('inf'), device=sequence_logits.device)

            try:
                ctc_loss = F.ctc_loss(
                    sequence_logits_ctc.log_softmax(dim=-1),
                    label,
                    input_lengths,
                    label_lgt,
                    blank=0,
                    reduction='mean',
                    zero_infinity=True
                )

                if torch.isnan(ctc_loss) or torch.isinf(ctc_loss):
                    if self._debug_step <= 5:
                        print(f"WARNING: Invalid CTC loss: {ctc_loss}")
                    return torch.tensor(0.0, device=sequence_logits.device, requires_grad=True)

                loss += ctc_loss

                if self._debug_step <= 5:
                    print(f"CTC loss: {ctc_loss.item():.4f}")

            except Exception as e:
                if self._debug_step <= 5:
                    print(f"CTC loss computation failed: {e}")
                return torch.tensor(0.0, device=sequence_logits.device, requires_grad=True)

        # 暂时禁用知识蒸馏，专注于基础CTC训练
        # TODO: 在基础功能正常后再启用知识蒸馏

        return loss

class TeacherStudentSLRModel(nn.Module):
    """
    教师-学生联合模型，用于更精细的知识蒸馏控制
    """
    def __init__(self, teacher_model_path, student_config):
        super(TeacherStudentSLRModel, self).__init__()
        
        # 加载教师模型
        self.teacher_model = CompleteModel()
        if teacher_model_path:
            checkpoint = torch.load(teacher_model_path, map_location='cpu')
            if "model_state_dict" in checkpoint:
                try:
                    self.teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
                    print("✅ Teacher model loaded successfully!")
                except:
                    print("⚠️ Teacher model structure mismatch, using random weights")
        
        # 冻结教师模型
        self.teacher_model.eval()
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # 创建学生模型
        self.student_model = StudentSLRModel(**student_config)
        
    def forward(self, vid, vid_lgt, label=None, label_lgt=None):
        """联合前向传播"""
        
        # 教师模型推理
        with torch.no_grad():
            if len(vid.shape) == 5:
                teacher_input = vid.permute(0, 2, 1, 3, 4)  # [B, C, T, H, W]
            else:
                teacher_input = vid
            teacher_logits = self.teacher_model(teacher_input)
        
        # 学生模型推理
        student_ret = self.student_model(vid, vid_lgt, label, label_lgt)
        
        # 添加教师模型输出
        student_ret['teacher_logits'] = teacher_logits
        
        return student_ret
    
    def criterion_calculation(self, ret_dict, label, label_lgt):
        """使用学生模型的损失计算"""
        return self.student_model.criterion_calculation(ret_dict, label, label_lgt)

# 为了兼容原框架，提供一个工厂函数
def create_student_model(num_classes, teacher_model_path=None, **kwargs):
    """
    创建学生模型的工厂函数
    
    Args:
        num_classes: 类别数
        teacher_model_path: 教师模型路径
        **kwargs: 其他参数
    """
    return StudentSLRModel(
        num_classes=num_classes,
        teacher_model_path=teacher_model_path,
        **kwargs
    )

# 兼容性别名
SLRModel = StudentSLRModel
