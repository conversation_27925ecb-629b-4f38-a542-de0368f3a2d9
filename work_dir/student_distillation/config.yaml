batch_size: 1
config: configs/student_distillation.yaml
dataset: phoenix2014-T
dataset_info:
  dataset_root: ./dataset/phoenix2014-T
  dict_path: ./preprocess/phoenix2014-T/gloss_dict.npy
  evaluation_dir: ./evaluation/slr_eval
  evaluation_prefix: phoenix2014-T-groundtruth
decode_mode: beam
device: '0'
eval_interval: 1
evaluate_tool: python
feeder: dataset.dataloader_video.BaseFeeder
feeder_args:
  datatype: video
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0
  input_size: 224
  mode: train
  num_gloss: -1
ignore_weights: []
load_checkpoints: null
load_weights: null
log_interval: 1000
loss_weights:
  ConvCTC: 0.0
  Dist: 0.0
  SeqCTC: 1.0
model: student_slr_network.StudentSLRModel
model_args:
  c2d_type: student
  conv_type: 2
  distillation_alpha: 0.6
  distillation_temperature: 5.0
  hidden_size: 512
  num_classes: 1116
  share_classifier: true
  teacher_model_path: ./dev_19.48_epoch35_model.pt
  use_bn: 1
  weight_norm: true
num_epoch: 20
num_worker: 4
optimizer_args:
  base_lr: 0.0005
  learning_ratio: 1
  nesterov: false
  optimizer: AdamW
  start_epoch: 0
  step:
  - 15
  - 25
  - 35
  weight_decay: 1.0e-05
phase: train
print_log: true
random_fix: true
random_seed: 726
save_interval: 5
test_batch_size: 1
work_dir: ./work_dir/student_distillation/
