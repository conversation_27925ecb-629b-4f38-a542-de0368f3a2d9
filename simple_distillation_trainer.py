import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from slr_network import SLRModel
from student_slr_network import StudentSLRModel


class SimpleKnowledgeDistillationLoss(nn.Module):
    """简单的知识蒸馏损失函数"""
    def __init__(self, alpha=0.5, temperature=4.0):
        super(SimpleKnowledgeDistillationLoss, self).__init__()
        self.alpha = alpha
        self.temperature = temperature
        self.ctc_loss = nn.CTCLoss(reduction='mean', zero_infinity=False)
        self.kl_div = nn.KLDivLoss(reduction='batchmean')
    
    def forward(self, student_outputs, teacher_outputs, labels, label_lengths, feat_lengths):
        """
        计算知识蒸馏损失
        Args:
            student_outputs: 学生模型输出字典
            teacher_outputs: 教师模型输出字典  
            labels: 真实标签
            label_lengths: 标签长度
            feat_lengths: 特征长度
        """
        # 1. 硬标签损失 (CTC Loss)
        student_logits = student_outputs["sequence_logits"]
        hard_loss = self.ctc_loss(
            student_logits.log_softmax(-1),
            labels.cpu().int(),
            feat_lengths.cpu().int(),
            label_lengths.cpu().int()
        )
        
        # 2. 软标签损失 (KL散度)
        teacher_logits = teacher_outputs["sequence_logits"]

        # 确保教师和学生输出的时序长度一致
        min_length = min(student_logits.size(0), teacher_logits.size(0))
        student_logits_aligned = student_logits[:min_length]
        teacher_logits_aligned = teacher_logits[:min_length]

        # 温度缩放
        student_soft = F.log_softmax(student_logits_aligned / self.temperature, dim=-1)
        teacher_soft = F.softmax(teacher_logits_aligned / self.temperature, dim=-1)

        # 重塑为2D进行KL散度计算
        T, B, C = student_soft.shape
        student_soft_2d = student_soft.view(-1, C)
        teacher_soft_2d = teacher_soft.view(-1, C)

        soft_loss = self.kl_div(student_soft_2d, teacher_soft_2d) * (self.temperature ** 2)
        
        # 3. 组合损失
        total_loss = self.alpha * hard_loss + (1 - self.alpha) * soft_loss
        
        return {
            'total_loss': total_loss,
            'hard_loss': hard_loss,
            'soft_loss': soft_loss
        }


class SimpleDistillationTrainer:
    """简单的知识蒸馏训练器"""
    def __init__(self, teacher_model_path, student_model_config, device='cuda', 
                 alpha=0.5, temperature=4.0):
        self.device = device
        self.alpha = alpha
        self.temperature = temperature
        
        # 加载教师模型
        print("正在加载教师模型...")
        self.teacher_model = self._load_teacher_model(teacher_model_path)
        self.teacher_model.eval()
        
        # 创建学生模型
        print("正在创建学生模型...")
        self.student_model = StudentSLRModel(**student_model_config)
        self.student_model.to(device)
        
        # 知识蒸馏损失函数
        self.distillation_loss = SimpleKnowledgeDistillationLoss(alpha, temperature)
        
        # 打印模型信息
        self._print_model_info()
    
    def _load_teacher_model(self, model_path):
        """加载教师模型"""
        # 创建教师模型实例
        teacher_config = {
            'num_classes': 1116,  # 根据权重文件调整
            'c2d_type': 'resnet18',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 1024,
            'weight_norm': True,
            'share_classifier': True,
            'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 25.0},
            'gloss_dict': {str(i): [i] for i in range(1116)}  # 创建简单词典
        }

        teacher_model = SLRModel(**teacher_config)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location='cpu')
        if "model_state_dict" in checkpoint:
            teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
        else:
            teacher_model.load_state_dict(checkpoint, strict=False)
        
        teacher_model.to(self.device)
        return teacher_model
    
    def _print_model_info(self):
        """打印模型信息"""
        teacher_params = sum(p.numel() for p in self.teacher_model.parameters())
        student_params = sum(p.numel() for p in self.student_model.parameters())
        
        print(f"\n=== 模型信息 ===")
        print(f"教师模型参数数量: {teacher_params:,}")
        print(f"学生模型参数数量: {student_params:,}")
        print(f"参数压缩率: {(1 - student_params/teacher_params)*100:.1f}%")
        print(f"蒸馏参数: alpha={self.alpha}, temperature={self.temperature}")
        print("=" * 50)
    
    def train_step(self, batch_data):
        """单步训练"""
        self.student_model.train()
        
        # 解析批次数据
        videos, labels, label_lengths, video_lengths = batch_data
        videos = videos.to(self.device)
        labels = labels.to(self.device)
        label_lengths = label_lengths.to(self.device)
        video_lengths = video_lengths.to(self.device)
        
        # 教师模型前向传播（无梯度）
        # 教师模型期望输入格式为 [B, T, C, H, W]
        teacher_videos = videos.permute(0, 2, 1, 3, 4)  # [B, C, T, H, W] -> [B, T, C, H, W]
        with torch.no_grad():
            teacher_outputs = self.teacher_model(teacher_videos, video_lengths, labels, label_lengths)

        # 学生模型前向传播
        # 学生模型期望输入格式为 [B, C, T, H, W]
        student_outputs = self.student_model(videos, video_lengths, labels, label_lengths)
        
        # 计算蒸馏损失
        loss_dict = self.distillation_loss(
            student_outputs, teacher_outputs, labels, label_lengths, 
            student_outputs["feat_len"]
        )
        
        return loss_dict, student_outputs, teacher_outputs
    
    def validate_step(self, batch_data):
        """验证步骤"""
        self.student_model.eval()
        
        with torch.no_grad():
            videos, labels, label_lengths, video_lengths = batch_data
            videos = videos.to(self.device)
            labels = labels.to(self.device)
            label_lengths = label_lengths.to(self.device)
            video_lengths = video_lengths.to(self.device)
            
            # 教师模型输出
            teacher_videos = videos.permute(0, 2, 1, 3, 4)  # [B, C, T, H, W] -> [B, T, C, H, W]
            teacher_outputs = self.teacher_model(teacher_videos, video_lengths, labels, label_lengths)

            # 学生模型输出
            student_outputs = self.student_model(videos, video_lengths, labels, label_lengths)
            
            # 计算损失
            loss_dict = self.distillation_loss(
                student_outputs, teacher_outputs, labels, label_lengths,
                student_outputs["feat_len"]
            )
            
            return loss_dict, student_outputs, teacher_outputs
    
    def save_student_model(self, save_path, epoch, optimizer_state=None, additional_info=None):
        """保存学生模型"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.student_model.state_dict(),
            'model_config': {
                'num_classes': self.student_model.num_classes,
                'hidden_size': 512,  # 学生模型的hidden_size
            },
            'distillation_config': {
                'alpha': self.alpha,
                'temperature': self.temperature
            }
        }
        
        if optimizer_state is not None:
            checkpoint['optimizer_state_dict'] = optimizer_state
        
        if additional_info is not None:
            checkpoint.update(additional_info)
        
        torch.save(checkpoint, save_path)
        print(f"学生模型已保存到: {save_path}")
    
    def load_student_checkpoint(self, checkpoint_path, optimizer=None):
        """加载学生模型检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.student_model.load_state_dict(checkpoint['model_state_dict'])
        
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        epoch = checkpoint.get('epoch', 0)
        print(f"已加载学生模型检查点，epoch: {epoch}")
        
        return epoch


def test_distillation_setup():
    """测试蒸馏设置"""
    print("测试知识蒸馏设置...")
    
    # 学生模型配置
    student_config = {
        'num_classes': 1116,  # 匹配教师模型
        'c2d_type': 'student',
        'conv_type': 2,
        'use_bn': True,
        'hidden_size': 512,
        'weight_norm': True,
        'share_classifier': True,
        'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 0.0, 'Dist': 0.0}
    }
    
    # 创建训练器
    trainer = SimpleDistillationTrainer(
        teacher_model_path='./dev_19.48_epoch35_model.pt',
        student_model_config=student_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        alpha=0.5,
        temperature=4.0
    )
    
    # 创建测试数据
    batch_size = 2
    seq_len = 16  # 增加时序长度以避免卷积核大小问题
    height, width = 224, 224
    num_classes = 1116  # 匹配教师模型

    videos = torch.randn(batch_size, 3, seq_len, height, width)
    labels = torch.randint(1, num_classes, (batch_size, 10))
    label_lengths = torch.tensor([10, 8])
    video_lengths = torch.tensor([seq_len, seq_len])
    
    batch_data = (videos, labels, label_lengths, video_lengths)
    
    # 测试训练步骤
    print("测试训练步骤...")
    loss_dict, student_out, teacher_out = trainer.train_step(batch_data)
    
    print(f"总损失: {loss_dict['total_loss']:.4f}")
    print(f"硬标签损失: {loss_dict['hard_loss']:.4f}")
    print(f"软标签损失: {loss_dict['soft_loss']:.4f}")
    
    print("知识蒸馏设置测试完成！")


if __name__ == "__main__":
    test_distillation_setup()
