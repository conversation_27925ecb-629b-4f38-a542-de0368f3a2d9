import pdb
import copy
import utils
import torch
import types
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from modules.criterions import SeqKD
from modules import BiLSTMLayer, TemporalConv
import modules.res124 as resnet
from typing import Optional, Tuple
import torch
import torch.utils.checkpoint
from torch import nn
import random
from transformers.activations import ACT2FN
from transformers import SwinConfig

import math
from modules.trans2 import Encoder
from student_model import StudentSignLanguageModel

# 导入教师模型
import sys
import os
sys.path.append('12_26_19.48_cnn_trans_1d')
sys.path.append(os.path.join(os.path.dirname(__file__), '12_26_19.48_cnn_trans_1d'))

# 尝试导入教师模型，如果失败则创建一个占位符
try:
    from complete_model import CompleteModel
except ImportError:
    try:
        # 尝试从12_26_19.48_cnn_trans_1d目录导入
        import importlib.util
        spec = importlib.util.spec_from_file_location("complete_model", "12_26_19.48_cnn_trans_1d/complete_model.py")
        complete_model_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(complete_model_module)
        CompleteModel = complete_model_module.CompleteModel
    except Exception as e:
        print(f"Warning: Could not import CompleteModel: {e}")
        # 创建一个占位符类
        class CompleteModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.dummy = nn.Linear(1, 1116)
            def forward(self, x):
                return torch.randn(x.shape[0], 1116)

class StudentSLRModel(nn.Module):
    """
    集成到原框架的学生模型
    保持与原SLRModel相同的接口，便于无缝替换
    """
    def __init__(
            self, num_classes, c2d_type='student', conv_type=2, use_bn=False,
            hidden_size=512, gloss_dict=None, loss_weights=None,
            weight_norm=True, share_classifier=True, teacher_model_path=None,
            distillation_alpha=0.5, distillation_temperature=4.0
    ):
        super(StudentSLRModel, self).__init__()
        
        # 基本参数
        self.num_classes = num_classes
        self.loss_weights = loss_weights
        self.gloss_dict = gloss_dict
        self.hidden_size = hidden_size
        
        # 知识蒸馏参数
        self.distillation_alpha = distillation_alpha
        self.distillation_temperature = distillation_temperature
        self.teacher_model_path = teacher_model_path
        
        # 学生模型核心
        self.student_backbone = StudentSignLanguageModel(
            num_classes=num_classes, 
            hidden_dim=256
        )
        
        # 为了兼容原框架，添加必要的组件
        self.conv1d = self.student_backbone.conv1d  # 用于kernel_size获取
        
        # CTC相关组件（保持与原模型一致）
        self.classifier = nn.Linear(512, num_classes)
        self.conv_classifier = nn.Linear(512, num_classes)
        
        # 损失函数
        self.loss_fn = torch.nn.CTCLoss(reduction='none', zero_infinity=False)
        
        # 教师模型（训练时加载）
        self.teacher_model = None
        self.load_teacher_model()
        
    def load_teacher_model(self):
        """加载教师模型"""
        if self.teacher_model_path and self.training:
            try:
                print(f"Loading teacher model from {self.teacher_model_path}")
                self.teacher_model = CompleteModel()
                checkpoint = torch.load(self.teacher_model_path, map_location='cpu')
                
                if "model_state_dict" in checkpoint:
                    # 尝试加载权重，如果失败就使用随机初始化的教师模型
                    try:
                        self.teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
                        print("✅ Teacher model weights loaded successfully!")
                    except Exception as e:
                        print(f"⚠️ Teacher model weight loading failed: {e}")
                        print("Using randomly initialized teacher model for distillation")
                
                # 冻结教师模型
                self.teacher_model.eval()
                for param in self.teacher_model.parameters():
                    param.requires_grad = False
                    
            except Exception as e:
                print(f"❌ Failed to load teacher model: {e}")
                self.teacher_model = None
    
    def forward(self, vid, vid_lgt, label=None, label_lgt=None):
        """
        前向传播，保持与原SLRModel相同的接口
        
        Args:
            vid: 视频数据 [B, T, C, H, W]
            vid_lgt: 视频长度 [B]
            label: 标签 (训练时使用)
            label_lgt: 标签长度 (训练时使用)
        """
        # 调整输入格式以适应学生模型
        # 原格式: [B, T, C, H, W] -> 学生模型格式: [B, C, T, H, W]
        if len(vid.shape) == 5:
            vid = vid.permute(0, 2, 1, 3, 4)
        
        # 学生模型前向传播
        student_logits, student_features = self.student_backbone(vid, return_features=True)
        
        # 为了兼容原框架，构造返回字典
        ret_dict = {
            'framewise_features': student_features['fused_features'].unsqueeze(1).expand(-1, vid.shape[2], -1),
            'visual_feat': student_features['fused_features'],
            'conv_logits': student_logits,
            'sequence_logits': student_logits,
            'conv_sents': student_logits,
            'sequence_sents': student_logits,
        }
        
        # 如果是训练模式且有教师模型，进行知识蒸馏
        if self.training and self.teacher_model is not None and label is not None:
            with torch.no_grad():
                teacher_logits = self.teacher_model(vid)
                ret_dict['teacher_logits'] = teacher_logits
        
        return ret_dict
    
    def criterion_calculation(self, ret_dict, label, label_lgt):
        """
        损失计算，集成知识蒸馏损失
        """
        loss = 0
        
        # 1. 原始CTC损失
        if 'conv_logits' in ret_dict:
            conv_pred = ret_dict['conv_logits']
            if conv_pred.dim() == 2:
                # 如果是2D，扩展为3D以适应CTC
                conv_pred = conv_pred.unsqueeze(1).expand(-1, max(label_lgt), -1)
            
            conv_pred = conv_pred.log_softmax(dim=-1)
            conv_pred = conv_pred.permute(1, 0, 2)  # [T, B, C] for CTC
            
            # 计算输入长度（假设所有帧都有效）
            input_lengths = torch.full((conv_pred.size(1),), conv_pred.size(0), dtype=torch.long)
            
            conv_loss = self.loss_fn(conv_pred, label, input_lengths, label_lgt).mean()
            loss += self.loss_weights.get('ConvCTC', 1.0) * conv_loss
        
        # 2. 序列CTC损失（如果存在）
        if 'sequence_logits' in ret_dict and 'sequence_logits' != 'conv_logits':
            seq_pred = ret_dict['sequence_logits']
            if seq_pred.dim() == 2:
                seq_pred = seq_pred.unsqueeze(1).expand(-1, max(label_lgt), -1)
            
            seq_pred = seq_pred.log_softmax(dim=-1)
            seq_pred = seq_pred.permute(1, 0, 2)
            
            input_lengths = torch.full((seq_pred.size(1),), seq_pred.size(0), dtype=torch.long)
            seq_loss = self.loss_fn(seq_pred, label, input_lengths, label_lgt).mean()
            loss += self.loss_weights.get('SeqCTC', 1.0) * seq_loss
        
        # 3. 知识蒸馏损失
        if 'teacher_logits' in ret_dict:
            teacher_logits = ret_dict['teacher_logits']
            student_logits = ret_dict['conv_logits']
            
            # 软标签损失（KL散度）
            soft_student = F.log_softmax(student_logits / self.distillation_temperature, dim=-1)
            soft_teacher = F.softmax(teacher_logits / self.distillation_temperature, dim=-1)
            
            distill_loss = F.kl_div(
                soft_student, soft_teacher, 
                reduction='batchmean'
            ) * (self.distillation_temperature ** 2)
            
            # 加权组合
            loss = self.distillation_alpha * loss + (1 - self.distillation_alpha) * distill_loss
            
            # 添加蒸馏损失权重
            loss += self.loss_weights.get('Dist', 1.0) * distill_loss * 0.1
        
        return loss

class TeacherStudentSLRModel(nn.Module):
    """
    教师-学生联合模型，用于更精细的知识蒸馏控制
    """
    def __init__(self, teacher_model_path, student_config):
        super(TeacherStudentSLRModel, self).__init__()
        
        # 加载教师模型
        self.teacher_model = CompleteModel()
        if teacher_model_path:
            checkpoint = torch.load(teacher_model_path, map_location='cpu')
            if "model_state_dict" in checkpoint:
                try:
                    self.teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
                    print("✅ Teacher model loaded successfully!")
                except:
                    print("⚠️ Teacher model structure mismatch, using random weights")
        
        # 冻结教师模型
        self.teacher_model.eval()
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # 创建学生模型
        self.student_model = StudentSLRModel(**student_config)
        
    def forward(self, vid, vid_lgt, label=None, label_lgt=None):
        """联合前向传播"""
        
        # 教师模型推理
        with torch.no_grad():
            if len(vid.shape) == 5:
                teacher_input = vid.permute(0, 2, 1, 3, 4)  # [B, C, T, H, W]
            else:
                teacher_input = vid
            teacher_logits = self.teacher_model(teacher_input)
        
        # 学生模型推理
        student_ret = self.student_model(vid, vid_lgt, label, label_lgt)
        
        # 添加教师模型输出
        student_ret['teacher_logits'] = teacher_logits
        
        return student_ret
    
    def criterion_calculation(self, ret_dict, label, label_lgt):
        """使用学生模型的损失计算"""
        return self.student_model.criterion_calculation(ret_dict, label, label_lgt)

# 为了兼容原框架，提供一个工厂函数
def create_student_model(num_classes, teacher_model_path=None, **kwargs):
    """
    创建学生模型的工厂函数
    
    Args:
        num_classes: 类别数
        teacher_model_path: 教师模型路径
        **kwargs: 其他参数
    """
    return StudentSLRModel(
        num_classes=num_classes,
        teacher_model_path=teacher_model_path,
        **kwargs
    )

# 兼容性别名
SLRModel = StudentSLRModel
