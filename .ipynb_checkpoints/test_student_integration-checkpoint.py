#!/usr/bin/env python3
"""
测试学生模型与原框架的集成
"""

import torch
import numpy as np
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, '12_26_19.48_cnn_trans_1d'))
sys.path.append('.')
sys.path.append('12_26_19.48_cnn_trans_1d')

def test_student_model_integration():
    """测试学生模型集成"""
    print("🧪 Testing Student Model Integration...")
    
    try:
        from student_slr_network import StudentSLRModel
        
        # 创建学生模型
        model = StudentSLRModel(
            num_classes=1116,
            teacher_model_path="dev_19.48_epoch35_model.pt",
            loss_weights={'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 10.0},
            distillation_alpha=0.5,
            distillation_temperature=4.0
        )
        
        print(f"✅ Student model created successfully")
        print(f"📊 Parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        batch_size = 2
        seq_len = 64
        height, width = 224, 224
        
        # 模拟输入数据 [B, T, C, H, W]
        vid = torch.randn(batch_size, seq_len, 3, height, width)
        vid_lgt = torch.tensor([seq_len, seq_len-10])
        
        # 模拟标签数据
        label = torch.tensor([1, 2, 3, 4, 5, 6, 7, 8])  # 示例标签
        label_lgt = torch.tensor([4, 4])  # 每个样本的标签长度
        
        print(f"📥 Input shape: {vid.shape}")
        print(f"📏 Video lengths: {vid_lgt}")
        print(f"🏷️ Label lengths: {label_lgt}")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            ret_dict = model(vid, vid_lgt, label, label_lgt)
        
        print(f"✅ Forward pass successful")
        print(f"📤 Output keys: {list(ret_dict.keys())}")
        
        for key, value in ret_dict.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
        
        # 测试损失计算
        model.train()  # 切换到训练模式以启用教师模型
        ret_dict = model(vid, vid_lgt, label, label_lgt)
        loss = model.criterion_calculation(ret_dict, label, label_lgt)
        
        print(f"✅ Loss calculation successful")
        print(f"📉 Loss value: {loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Student model integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n🔧 Testing Configuration Loading...")
    
    try:
        import yaml
        
        config_path = "configs/student_distillation.yaml"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = yaml.load(f, Loader=yaml.FullLoader)
        
        print(f"✅ Config loaded successfully")
        print(f"📁 Model: {config['model']}")
        print(f"🎯 Batch size: {config['batch_size']}")
        print(f"🔄 Epochs: {config['num_epoch']}")
        print(f"📊 Loss weights: {config['loss_weights']}")
        
        # 检查关键配置
        required_keys = ['model', 'model_args', 'loss_weights', 'optimizer_args']
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing required config key: {key}")
                return False
        
        # 检查模型参数
        model_args = config['model_args']
        distill_keys = ['teacher_model_path', 'distillation_alpha', 'distillation_temperature']
        for key in distill_keys:
            if key in model_args:
                print(f"  {key}: {model_args[key]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading test failed: {e}")
        return False

def test_data_compatibility():
    """测试数据兼容性"""
    print("\n📊 Testing Data Compatibility...")
    
    try:
        # 模拟原框架的数据格式
        batch_size = 2
        max_frames = 100
        
        # 原框架数据格式
        padded_video = torch.randn(batch_size, max_frames, 3, 224, 224)  # [B, T, C, H, W]
        video_length = torch.tensor([80, 60])  # 实际视频长度
        padded_label = torch.tensor([1, 2, 3, 4, 5, 6, 7, 8])  # 连接的标签
        label_length = torch.tensor([4, 4])  # 每个样本的标签长度
        info = ["sample1|info", "sample2|info"]  # 样本信息
        
        print(f"✅ Data format simulation successful")
        print(f"📹 Video shape: {padded_video.shape}")
        print(f"📏 Video lengths: {video_length}")
        print(f"🏷️ Label shape: {padded_label.shape}")
        print(f"📏 Label lengths: {label_length}")
        
        # 测试学生模型是否能处理这种格式
        from student_slr_network import StudentSLRModel
        
        model = StudentSLRModel(
            num_classes=1116,
            teacher_model_path=None,  # 不加载教师模型以加快测试
            loss_weights={'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 10.0}
        )
        
        model.eval()
        with torch.no_grad():
            ret_dict = model(padded_video, video_length, padded_label, label_length)
        
        print(f"✅ Student model handles original data format correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Data compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Starting Student Model Integration Tests...\n")
    
    tests = [
        ("Student Model Integration", test_student_model_integration),
        ("Configuration Loading", test_config_loading),
        ("Data Compatibility", test_data_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: PASSED\n")
        else:
            print(f"❌ {test_name}: FAILED\n")
    
    # 总结
    print(f"{'='*50}")
    print("📋 Test Summary")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your student model is ready for training.")
        print("\n📝 Next steps:")
        print("1. Prepare your dataset")
        print("2. Run: python train_student_model.py --config configs/student_distillation.yaml --device 0")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
