#!/usr/bin/env python3
"""
测试数据格式，确保我们理解数据加载器的输出格式
"""

import torch
import numpy as np
import yaml
from dataset.dataloader_video import BaseFeeder


def test_data_format():
    """测试数据格式"""
    print("测试数据格式...")
    
    # 加载配置
    with open('configs/student_distillation.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # 加载数据集信息
    dataset_config_path = f"./configs/{config['dataset']}.yaml"
    with open(dataset_config_path, 'r') as f:
        dataset_info = yaml.safe_load(f)
    
    # 加载词典
    gloss_dict = np.load(dataset_info['dict_path'], allow_pickle=True).item()
    print(f"词典大小: {len(gloss_dict)}")
    
    # 创建数据集
    feeder_args = config['feeder_args'].copy()
    feeder_args['prefix'] = dataset_info['dataset_root']
    feeder_args['mode'] = 'train'
    feeder_args['transform_mode'] = True
    feeder_args['dataset'] = config['dataset']
    
    dataset = BaseFeeder(
        gloss_dict=gloss_dict,
        kernel_size=['K5', 'K5'],
        **feeder_args
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 创建数据加载器
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=2,
        shuffle=False,
        num_workers=0,
        drop_last=False,
        pin_memory=False,
        collate_fn=BaseFeeder.collate_fn
    )
    
    # 测试几个批次
    for batch_idx, batch_data in enumerate(data_loader):
        if batch_idx >= 3:  # 只测试前3个批次
            break
            
        print(f"\n=== 批次 {batch_idx} ===")
        
        # 解析数据
        if len(batch_data) == 5:
            videos, video_lengths, labels, label_lengths, info = batch_data
        else:
            print(f"意外的批次数据格式: {len(batch_data)} 个元素")
            continue
        
        print(f"视频形状: {videos.shape}")
        print(f"视频长度: {video_lengths}")
        print(f"标签形状: {labels.shape if hasattr(labels, 'shape') else len(labels)}")
        print(f"标签长度: {label_lengths}")
        print(f"信息: {len(info) if info else 0}")
        
        # 检查视频数据的值范围
        print(f"视频数据范围: [{videos.min():.3f}, {videos.max():.3f}]")
        
        # 分析维度
        if videos.dim() == 5:
            B, dim1, dim2, H, W = videos.shape
            print(f"维度分析: B={B}, dim1={dim1}, dim2={dim2}, H={H}, W={W}")
            
            if dim2 == 3:
                print("推测格式: [B, T, C, H, W]")
            elif dim1 == 3:
                print("推测格式: [B, C, T, H, W]")
            else:
                print("未知格式，需要进一步分析")
        
        # 测试一个样本
        if batch_idx == 0:
            print(f"\n详细分析第一个样本:")
            sample_video = videos[0]
            print(f"样本形状: {sample_video.shape}")
            
            # 尝试不同的维度解释
            if sample_video.dim() == 4:
                if sample_video.shape[1] == 3:
                    print("样本格式: [T, C, H, W]")
                elif sample_video.shape[0] == 3:
                    print("样本格式: [C, T, H, W]")
                else:
                    print("样本格式: 未知")


if __name__ == "__main__":
    test_data_format()
