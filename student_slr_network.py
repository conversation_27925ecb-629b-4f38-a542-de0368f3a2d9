import pdb
import copy
import utils
import torch
import types
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from modules.criterions import SeqKD
from modules import BiLSTMLayer, TemporalConv
import modules.res124 as resnet
from typing import Optional, Tuple
import torch
import torch.utils.checkpoint
from torch import nn
import random
from transformers.activations import ACT2FN
from transformers import SwinConfig

import math
from modules.trans2 import Encoder
config = SwinConfig.from_json_file("SwinConfig.json")


# ==================== 学生模型组件定义 ====================

class LightweightBasicBlock(nn.Module):
    """轻量级3D ResNet基础块"""
    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(LightweightBasicBlock, self).__init__()
        # 使用深度可分离卷积减少参数
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=(1, 3, 3), 
                              stride=(1, stride, stride), padding=(0, 1, 1), bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=(1, 3, 3), 
                              stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        if self.downsample is not None:
            residual = self.downsample(x)
            
        out += residual
        out = self.relu(out)
        
        return out


class LightweightECA(nn.Module):
    """轻量级ECA注意力模块"""
    def __init__(self, channels, gamma=2, b=1):
        super(LightweightECA, self).__init__()
        t = int(abs((math.log(channels, 2) + b) / gamma))
        k = t if t % 2 else t + 1
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=k//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        y = self.avg_pool(x)
        y = self.conv(y.squeeze(-1).squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1).unsqueeze(-1)
        y = self.sigmoid(y)
        return x * y.expand_as(x)


class LightweightSpatialCorr(nn.Module):
    """简化的空间相关性模块"""
    def __init__(self, channels):
        super(LightweightSpatialCorr, self).__init__()
        reduction_channels = max(8, channels // 16)
        self.down_conv = nn.Conv3d(channels, reduction_channels, kernel_size=1, bias=False)
        self.spatial_conv = nn.Conv3d(reduction_channels, reduction_channels, 
                                    kernel_size=(3, 3, 3), padding=(1, 1, 1), bias=False)
        self.up_conv = nn.Conv3d(reduction_channels, channels, kernel_size=1, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        identity = x
        out = self.down_conv(x)
        out = self.spatial_conv(out)
        out = self.up_conv(out)
        out = self.sigmoid(out)
        return identity * out


class LightweightCNN3D(nn.Module):
    """轻量级3D CNN模块"""
    def __init__(self, output_channels=256):
        super(LightweightCNN3D, self).__init__()
        
        # 初始卷积层 - 减少通道数
        self.conv1 = nn.Conv3d(3, 32, kernel_size=(1, 7, 7), stride=(1, 2, 2), 
                              padding=(0, 3, 3), bias=False)
        self.bn1 = nn.BatchNorm3d(32)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1))
        
        # ECA注意力
        self.eca1 = LightweightECA(32)
        
        # ResNet层 - 通道数减半
        self.layer1 = self._make_layer(32, 32, 2)
        self.layer2 = self._make_layer(32, 64, 2, stride=2)
        self.corr1 = LightweightSpatialCorr(64)
        
        self.layer3 = self._make_layer(64, 128, 2, stride=2)
        self.corr2 = LightweightSpatialCorr(128)
        
        self.layer4 = self._make_layer(128, output_channels, 2, stride=2)
        self.eca2 = LightweightECA(output_channels)
        
        # 全局平均池化
        self.avgpool = nn.AdaptiveAvgPool3d((None, 1, 1))
        
    def _make_layer(self, inplanes, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or inplanes != planes:
            downsample = nn.Sequential(
                nn.Conv3d(inplanes, planes, kernel_size=1, stride=(1, stride, stride), bias=False),
                nn.BatchNorm3d(planes),
            )
        
        layers = []
        layers.append(LightweightBasicBlock(inplanes, planes, stride, downsample))
        for _ in range(1, blocks):
            layers.append(LightweightBasicBlock(planes, planes))
            
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # 输入: [B, 3, T, H, W]
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        x = self.eca1(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.corr1(x)
        
        x = self.layer3(x)
        x = self.corr2(x)
        
        x = self.layer4(x)
        x = self.eca2(x)
        
        # 空间池化，保留时间维度
        x = self.avgpool(x)  # [B, output_channels, T, 1, 1]
        x = x.squeeze(-1).squeeze(-1)  # [B, output_channels, T]
        
        return x


class LightweightTemporalConv(nn.Module):
    """轻量级1D时序卷积模块"""
    def __init__(self, input_size=256, hidden_size=512, num_classes=-1):
        super(LightweightTemporalConv, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_classes = num_classes
        
        # 简化的时序卷积 - 只用两层
        self.temporal_conv = nn.Sequential(
            nn.Conv1d(input_size, hidden_size, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(hidden_size),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Conv1d(hidden_size, hidden_size, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(hidden_size),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3)
        )
        
        if self.num_classes != -1:
            self.fc = nn.Linear(self.hidden_size, self.num_classes)
    
    def forward(self, frame_feat, lgt):
        visual_feat = self.temporal_conv(frame_feat)
        # 长度不变，因为使用了padding=2
        logits = None if self.num_classes == -1 else self.fc(visual_feat.transpose(1, 2)).transpose(1, 2)
        return {
            "visual_feat": visual_feat.permute(2, 0, 1),
            "conv_logits": logits.permute(2, 0, 1) if logits is not None else None,
            "feat_len": lgt.cpu(),
        }


class LightweightTransformer(nn.Module):
    """轻量级Transformer模块"""
    def __init__(self, d_model=256, num_layers=1, nhead=4):
        super(LightweightTransformer, self).__init__()
        self.d_model = d_model

        # 如果输入维度与配置不匹配，添加投影层
        self.input_projection = None
        if d_model != 512:  # 原始配置的d_model是512
            self.input_projection = nn.Linear(d_model, 512)
            self.output_projection = nn.Linear(512, d_model)

        # 使用原始配置，但调整层数和注意力头数
        self.config = copy.deepcopy(config)
        self.config.decoder_layers = num_layers
        self.config.decoder_attention_heads = nhead
        self.config.decoder_ffn_dim = 512 * 2  # 保持原始比例

        self.transformer = Encoder(self.config)

    def forward(self, x):
        # x: [B, C, T] -> [B, T, C]
        x = x.permute(0, 2, 1)

        # 如果需要，投影到512维
        if self.input_projection is not None:
            x = self.input_projection(x)

        x = self.transformer(x)

        # 如果需要，投影回原始维度
        if self.input_projection is not None:
            x = self.output_projection(x)

        # [B, T, C] -> [B, C, T]
        return x.permute(0, 2, 1)


# ==================== 学生模型主类 ====================

class Identity(nn.Module):
    def __init__(self):
        super(Identity, self).__init__()

    def forward(self, x):
        return x


class NormLinear(nn.Module):
    def __init__(self, in_dim, out_dim):
        super(NormLinear, self).__init__()
        self.weight = nn.Parameter(torch.Tensor(in_dim, out_dim))
        nn.init.xavier_uniform_(self.weight, gain=nn.init.calculate_gain('relu'))

    def forward(self, x):
        outputs = torch.matmul(x, F.normalize(self.weight, dim=0))
        return outputs


class StudentSLRModel(nn.Module):
    """学生手语识别模型"""
    def __init__(
            self, num_classes, c2d_type='student', conv_type=2, use_bn=False,
            hidden_size=512, gloss_dict=None, loss_weights=None,
            weight_norm=True, share_classifier=True, **kwargs
    ):
        super(StudentSLRModel, self).__init__()
        self.decoder = None
        self.loss = dict()
        self.criterion_init()
        self.num_classes = num_classes
        # 设置默认的loss_weights
        self.loss_weights = loss_weights if loss_weights is not None else {'SeqCTC': 1.0}
        
        # 学生模型的轻量级组件
        cnn_output_channels = 256  # 减少通道数
        self.conv2d = LightweightCNN3D(output_channels=cnn_output_channels)
        
        self.conv1d = LightweightTemporalConv(
            input_size=cnn_output_channels,
            hidden_size=hidden_size,
            num_classes=num_classes
        )

        # 创建解码器，如果gloss_dict为None则创建一个简单的字典
        if gloss_dict is None:
            # 创建一个简单的词典用于测试
            gloss_dict = {str(i): [i] for i in range(num_classes)}
        self.decoder = utils.Decode(gloss_dict, num_classes, 'beam')
        
        self.temporal_model = LightweightTransformer(
            d_model=cnn_output_channels,  # 匹配3D CNN输出
            num_layers=1,  # 减少层数
            nhead=4        # 减少注意力头数
        )
        
        if weight_norm:
            self.classifier = NormLinear(hidden_size, self.num_classes)
            self.conv1d.fc = NormLinear(hidden_size, self.num_classes)
        else:
            self.classifier = nn.Linear(hidden_size, self.num_classes)
            self.conv1d.fc = nn.Linear(hidden_size, self.num_classes)
        if share_classifier:
            self.conv1d.fc = self.classifier

    def forward(self, x, len_x, label=None, label_lgt=None):
        if len(x.shape) == 5:
            # videos: [B, 3, T, H, W]
            batch, channel, temp, height, width = x.shape
            # 3D CNN特征提取
            framewise = self.conv2d(x)  # [B, 256, T]
        else:
            # frame-wise features
            framewise = x
        
        # Transformer处理
        framewise = framewise.permute(0, 2, 1)  # [B, T, 256]
        tm_outputs = self.temporal_model(framewise.permute(0, 2, 1))  # [B, 256, T]
        
        # 1D卷积处理
        conv1d_outputs = self.conv1d(tm_outputs, len_x)
        
        # 分类
        x = conv1d_outputs['visual_feat']  # [T, B, C]
        lgt = conv1d_outputs['feat_len']
        outputs = self.classifier(x)
        
        pred = None if self.training else self.decoder.decode(outputs, lgt, batch_first=False, probs=False)
        conv_pred = None if self.training else self.decoder.decode(conv1d_outputs['conv_logits'], lgt, batch_first=False, probs=False)

        return {
            "feat_len": lgt,
            "conv_logits": conv1d_outputs['conv_logits'],
            "sequence_logits": outputs,
            "conv_sents": conv_pred,
            "recognized_sents": pred,
        }

    def criterion_calculation(self, ret_dict, label, label_lgt):
        loss = 0
        for k, weight in self.loss_weights.items():
            if k == 'ConvCTC':
                loss += weight * self.loss['CTCLoss'](ret_dict["conv_logits"].log_softmax(-1),
                                                      label.cpu().int(), ret_dict["feat_len"].cpu().int(),
                                                      label_lgt.cpu().int()).mean()
            elif k == 'SeqCTC':
                loss += weight * self.loss['CTCLoss'](ret_dict["sequence_logits"].log_softmax(-1),
                                                      label.cpu().int(), ret_dict["feat_len"].cpu().int(),
                                                      label_lgt.cpu().int()).mean()
            elif k == 'Dist':
                loss += weight * self.loss['distillation'](ret_dict["conv_logits"],
                                                           ret_dict["sequence_logits"].detach(),
                                                           use_blank=False)
        return loss

    def criterion_init(self):
        self.loss['CTCLoss'] = torch.nn.CTCLoss(reduction='none', zero_infinity=False)
        self.loss['distillation'] = SeqKD(T=8)
        return self.loss
