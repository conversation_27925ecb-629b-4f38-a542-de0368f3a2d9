#!/usr/bin/env python3
"""
独立验证脚本 - 完全按照教师模型的方式进行验证
直接创建学生模型，使用seq_eval函数
"""

import os
import sys
import yaml
import torch
import numpy as np
import argparse
import importlib
import utils
from seq_scripts import seq_eval


def import_class(name):
    """导入类 - 与main.py中的函数一致"""
    components = name.rsplit('.', 1)
    mod = importlib.import_module(components[0])
    mod = getattr(mod, components[1])
    return mod


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loader(config, dataset_info, gloss_dict, kernel_sizes, mode='dev'):
    """创建数据加载器 - 完全按照main.py的方式"""
    # 完全按照main.py第204-210行的方式
    feeder_args = config['feeder_args'].copy()
    feeder_args["prefix"] = dataset_info['dataset_root']
    feeder_args["mode"] = mode.split("_")[0]  # 处理train_eval这样的模式
    feeder_args["transform_mode"] = False  # 验证时不做数据增强

    # 动态导入feeder类 - 与main.py一致
    feeder_class = import_class(config['feeder'])

    # 创建数据集 - 完全按照main.py第209行的方式
    dataset = feeder_class(
        gloss_dict=gloss_dict,
        kernel_size=kernel_sizes,  # 关键：传入kernel_size
        dataset=config['dataset'],
        **feeder_args
    )

    # 创建数据加载器 - 完全按照main.py第214-224行的方式
    def init_fn(worker_id):
        np.random.seed(int(config.get('random_seed', 0)) + worker_id)

    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=config['test_batch_size'],
        shuffle=False,  # 验证时不打乱
        drop_last=False,
        num_workers=config['num_worker'],
        collate_fn=feeder_class.collate_fn,
        pin_memory=True,
        worker_init_fn=init_fn,
    )

    return data_loader, dataset


def create_student_model(config, dataset_info, gloss_dict):
    """创建学生模型 - 完全按照main.py的方式"""
    print("Loading student model...")

    # 修改模型配置为学生模型
    model_args = config['model_args'].copy()
    model_args['c2d_type'] = 'student'  # 强制使用学生模型

    # 动态导入学生模型类
    model_class = import_class('student_slr_network.StudentSLRModel')

    # 创建学生模型 - 完全按照main.py第129-133行的方式
    model = model_class(
        **model_args,
        gloss_dict=gloss_dict,  # 传入真实词典
        loss_weights=config['loss_weights'],
    )

    print("Student model created successfully.")
    return model


def validate_model(model, data_loader, device_obj, config, work_dir="./work_dir/", mode="dev"):
    """验证模型 - 直接调用seq_eval函数"""
    print(f"开始验证 {mode} 集...")

    # 创建一个简单的配置对象，模拟main.py中的args
    class SimpleConfig:
        def __init__(self, config, dataset_info):
            self.dataset_info = dataset_info
            self.work_dir = work_dir
            self.evaluate_tool = "python"

    simple_config = SimpleConfig(config, config['dataset_info'])

    # 创建一个简单的记录器
    class SimpleRecorder:
        def record_timer(self, name):
            pass
        def print_log(self, msg, file_path=None):
            print(msg)

    recoder = SimpleRecorder()

    # 直接调用seq_eval函数 - 完全按照main.py第70-71行的方式
    print("🔍 调用seq_eval函数进行验证...")
    wer = seq_eval(
        cfg=simple_config,
        loader=data_loader,
        model=model,
        device=device_obj,
        mode=mode,
        epoch=9999,  # 使用一个特殊的epoch号
        work_dir=work_dir,
        recoder=recoder,
        evaluate_tool="python"
    )

    return wer


def main():
    """主函数 - 完全按照main.py的方式"""
    parser = argparse.ArgumentParser(description='独立验证脚本')
    parser.add_argument('--config', default='configs/student_distillation.yaml', help='配置文件路径')
    parser.add_argument('--student_weights', required=True, help='学生模型权重路径')
    parser.add_argument('--device', default='0', help='GPU设备')
    parser.add_argument('--mode', default='dev', choices=['dev', 'test'], help='验证模式')
    parser.add_argument('--work_dir', default='./work_dir/validation_only/', help='工作目录')

    args = parser.parse_args()

    print(f"🚀 独立验证脚本启动 - 完全按照教师模型方式")
    print(f"学生模型权重: {args.student_weights}")
    print(f"验证模式: {args.mode}")

    # 加载配置
    config = load_config(args.config)

    # 加载数据集信息 - 完全按照main.py第251-252行的方式
    with open(f"./configs/{config['dataset']}.yaml", 'r') as f:
        dataset_info = yaml.safe_load(f)
    config['dataset_info'] = dataset_info

    # 加载词典 - 完全按照main.py第50行的方式
    gloss_dict = np.load(dataset_info['dict_path'], allow_pickle=True).item()
    print(f"加载词典: {len(gloss_dict)} 个词汇")

    # 设置设备 - 完全按照main.py第126行的方式
    device_obj = utils.GpuDataParallel()
    device_obj.set_device(args.device)
    print(f"使用设备: cuda:{args.device}")

    # 创建学生模型 - 完全按照main.py的方式
    model = create_student_model(config, dataset_info, gloss_dict)

    # 加载学生模型权重
    print(f"加载学生模型权重: {args.student_weights}")
    checkpoint = torch.load(args.student_weights, map_location='cpu')
    if "model_state_dict" in checkpoint:
        model.load_state_dict(checkpoint["model_state_dict"], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)

    # 将模型移动到设备 - 完全按照main.py第141行的方式
    model = model.to(device_obj.output_device)
    print("Model moved to device.")

    # 获取kernel_sizes - 完全按照main.py第142行的方式
    kernel_sizes = model.conv1d.kernel_size
    print(f"Kernel sizes: {kernel_sizes}")

    # 创建数据加载器 - 完全按照main.py的方式
    data_loader, dataset = create_data_loader(config, dataset_info, gloss_dict, kernel_sizes, args.mode)
    print(f"{args.mode}集大小: {len(dataset)}")

    # 进行验证 - 直接调用seq_eval
    wer = validate_model(model, data_loader, device_obj, config, args.work_dir, args.mode)

    print(f"\n🎉 验证完成！")
    print(f"最终WER: {wer:.2f}%")

    if wer < 90:
        print("✅ 验证流程正确！WER < 90%，说明模型有学习能力")
    else:
        print("❌ 可能存在问题，WER过高")


if __name__ == "__main__":
    main()
