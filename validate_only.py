#!/usr/bin/env python3
"""
独立验证脚本 - 只进行验证集评估，不训练
用于测试验证流程是否正确
"""

import os
import sys
import yaml
import torch
import numpy as np
import argparse
from advanced_distillation_trainer import AdvancedDistillationTrainer
from dataset.dataloader_video import BaseFeeder
from evaluation.slr_eval.wer_calculation import evaluate
import utils


def write2file(path, info, output):
    """将预测结果写入CTM文件 - 与seq_scripts.py中的标准函数一致"""
    filereader = open(path, "w")
    for sample_idx, sample in enumerate(output):
        for word_idx, word in enumerate(sample):
            filereader.writelines(
                "{} 1 {:.2f} {:.2f} {}\n".format(info[sample_idx],
                                                 word_idx * 1.0 / 100,
                                                 (word_idx + 1) * 1.0 / 100,
                                                 word[0]))
    filereader.close()


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_data_loader(config, dataset_info, gloss_dict, mode='dev'):
    """创建数据加载器"""
    feeder_args = config['feeder_args'].copy()
    
    # 添加必需的参数
    feeder_args['prefix'] = dataset_info['dataset_root']
    feeder_args['mode'] = mode.split("_")[0]  # 处理train_eval这样的模式
    feeder_args['transform_mode'] = False  # 验证时不做数据增强
    feeder_args['dataset'] = config['dataset']
    
    # 动态导入feeder类
    feeder_class = config['feeder']
    module_name, class_name = feeder_class.rsplit('.', 1)
    module = __import__(module_name, fromlist=[class_name])
    feeder_cls = getattr(module, class_name)
    
    # 创建数据集
    dataset = feeder_cls(
        gloss_dict=gloss_dict,
        kernel_size=['K5', 'K5'],
        **feeder_args
    )
    
    # 创建数据加载器
    batch_size = config['test_batch_size']
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=False,  # 验证时不打乱
        num_workers=config['num_worker'],
        drop_last=False,
        pin_memory=True,
        collate_fn=feeder_cls.collate_fn if hasattr(feeder_cls, 'collate_fn') else None
    )
    
    return data_loader, dataset


def validate_model(trainer, data_loader, device, config, work_dir="./work_dir/", mode="dev"):
    """验证模型 - 完全按照seq_eval的标准方法"""
    print(f"开始验证 {mode} 集...")
    
    # 确保模型处于评估状态
    trainer.student_model.eval()
    
    # 按照seq_eval的标准方法收集数据
    total_sent = []      # 学生模型预测结果
    total_conv_sent = [] # 学生模型conv预测结果  
    total_info = []      # 文件信息
    
    # 创建device对象
    device_obj = utils.GpuDataParallel()
    device_obj.set_device(device)
    
    with torch.no_grad():
        for batch_idx, data in enumerate(data_loader):
            try:
                # 完全按照seq_eval的方法处理数据
                vid = device_obj.data_to_device(data[0])
                vid_lgt = device_obj.data_to_device(data[1])
                label = device_obj.data_to_device(data[2])
                label_lgt = device_obj.data_to_device(data[3])
                
                # 调试信息
                if batch_idx < 3:
                    print(f"验证批次 {batch_idx}:")
                    print(f"  原始vid.shape: {vid.shape}")
                    print(f"  vid_lgt: {vid_lgt}")
                
                # 关键修复：学生模型需要 [B, C, T, H, W] 格式
                if len(vid.shape) == 5:
                    vid_student = vid.permute(0, 2, 1, 3, 4)
                    if batch_idx < 3:
                        print(f"  转换后vid_student.shape: {vid_student.shape}")
                else:
                    vid_student = vid
                
                # 确保模型在eval模式下进行推理
                trainer.student_model.eval()
                ret_dict = trainer.student_model(vid_student, vid_lgt, label=label, label_lgt=label_lgt)
                
                if batch_idx < 3:
                    print(f"  模型推理成功")
                    print(f"  recognized_sents数量: {len(ret_dict['recognized_sents']) if ret_dict['recognized_sents'] else 0}")
                    print(f"  conv_sents数量: {len(ret_dict['conv_sents']) if ret_dict['conv_sents'] else 0}")
                    
                    # 详细检查预测结果内容
                    if ret_dict['recognized_sents'] and len(ret_dict['recognized_sents']) > 0:
                        print(f"  第一个样本的预测结果: {ret_dict['recognized_sents'][0]}")
                        if len(ret_dict['recognized_sents']) > 1:
                            print(f"  第二个样本的预测结果: {ret_dict['recognized_sents'][1]}")
                    else:
                        print(f"  ❌ recognized_sents为空!")
                
                # 收集预测结果
                total_info += [file_name.split("|")[0] for file_name in data[-1]]
                total_sent += ret_dict['recognized_sents']
                total_conv_sent += ret_dict['conv_sents']
                
            except Exception as e:
                print(f"验证批次 {batch_idx} 出错: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    # 计算WER
    try:
        print(f"\n📊 验证结果统计:")
        print(f"收集到 {len(total_sent)} 个学生模型预测")
        print(f"收集到 {len(total_conv_sent)} 个学生模型conv预测")
        print(f"收集到 {len(total_info)} 个文件信息")
        
        # 确保work_dir存在
        os.makedirs(work_dir, exist_ok=True)
        
        # 写入文件
        python_eval = True
        write2file(work_dir + "output-hypothesis-{}.ctm".format(mode), total_info, total_sent)
        write2file(work_dir + "output-hypothesis-{}-conv.ctm".format(mode), total_info, total_conv_sent)
        
        # 计算WER
        conv_ret = evaluate(
            prefix=work_dir, mode=mode, output_file="output-hypothesis-{}-conv.ctm".format(mode),
            evaluate_dir=config['dataset_info']['evaluation_dir'],
            evaluate_prefix=config['dataset_info']['evaluation_prefix'],
            output_dir="validation_only_result/",
            python_evaluate=python_eval,
        )
        student_wer = evaluate(
            prefix=work_dir, mode=mode, output_file="output-hypothesis-{}.ctm".format(mode),
            evaluate_dir=config['dataset_info']['evaluation_dir'],
            evaluate_prefix=config['dataset_info']['evaluation_prefix'],
            output_dir="validation_only_result/",
            python_evaluate=python_eval,
            triplet=True,
        )
        
        print(f"\n🎯 最终结果:")
        print(f"学生模型WER: {student_wer:.2f}%")
        print(f"学生模型Conv WER: {conv_ret:.2f}%")
        
        return student_wer
        
    except Exception as e:
        print(f"WER评估计算出错: {e}")
        import traceback
        traceback.print_exc()
        return 100.0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='独立验证脚本')
    parser.add_argument('--config', default='configs/student_distillation.yaml', help='配置文件路径')
    parser.add_argument('--student_weights', required=True, help='学生模型权重路径')
    parser.add_argument('--teacher_model', default='dev_19.48_epoch35_model.pt', help='教师模型路径')
    parser.add_argument('--device', default='0', help='GPU设备')
    parser.add_argument('--mode', default='dev', choices=['dev', 'test'], help='验证模式')
    parser.add_argument('--work_dir', default='./work_dir/validation_only/', help='工作目录')
    
    args = parser.parse_args()
    
    print(f"🚀 独立验证脚本启动")
    print(f"学生模型权重: {args.student_weights}")
    print(f"教师模型权重: {args.teacher_model}")
    print(f"验证模式: {args.mode}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 加载数据集信息
    dataset_config_path = f"./configs/{config['dataset']}.yaml"
    with open(dataset_config_path, 'r') as f:
        dataset_info = yaml.safe_load(f)
    config['dataset_info'] = dataset_info
    
    # 加载词典
    gloss_dict = np.load(dataset_info['dict_path'], allow_pickle=True).item()
    print(f"加载词典: {len(gloss_dict)} 个词汇")
    
    # 设置设备
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 学生模型配置
    student_config = {
        'num_classes': len(gloss_dict) + 1,
        'c2d_type': 'student',
        'conv_type': config['model_args']['conv_type'],
        'use_bn': config['model_args']['use_bn'],
        'hidden_size': config['model_args'].get('hidden_size', 512),
        'weight_norm': config['model_args'].get('weight_norm', True),
        'share_classifier': config['model_args'].get('share_classifier', True),
        'loss_weights': config['loss_weights'],
        'gloss_dict': gloss_dict
    }
    
    # 创建训练器
    trainer = AdvancedDistillationTrainer(
        teacher_model_path=args.teacher_model,
        student_model_config=student_config,
        device=device,
        alpha=0.8,
        temperature=2.0,
        feature_weight=0.0001
    )
    
    # 加载学生模型权重
    print(f"加载学生模型权重: {args.student_weights}")
    checkpoint = torch.load(args.student_weights, map_location='cpu')
    if "model_state_dict" in checkpoint:
        trainer.student_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
    else:
        trainer.student_model.load_state_dict(checkpoint, strict=False)
    
    # 创建数据加载器
    data_loader, dataset = create_data_loader(config, dataset_info, gloss_dict, args.mode)
    print(f"{args.mode}集大小: {len(dataset)}")
    
    # 进行验证
    wer = validate_model(trainer, data_loader, device, config, args.work_dir, args.mode)
    
    print(f"\n🎉 验证完成！")
    print(f"最终WER: {wer:.2f}%")
    
    if wer < 90:
        print("✅ 验证流程正确！WER < 90%，说明模型有学习能力")
    else:
        print("❌ 可能存在问题，WER过高")


if __name__ == "__main__":
    main()
