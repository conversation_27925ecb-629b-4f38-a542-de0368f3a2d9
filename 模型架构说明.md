# 手语识别模型架构说明

本文档详细解析了权重文件 `dev_19.48_epoch35_model.pt` 中的模型架构。该模型是一个混合架构，结合了3D CNN、1D CNN和Transformer，用于手语识别任务。

## 模型概览

模型由四个主要组件组成：

1. **3D CNN模块（conv2d）**：提取视频的时空特征
2. **1D时序卷积模块（conv1d）**：进一步处理时序信息
3. **Transformer模块（temporal_model）**：捕捉长序列依赖关系
4. **分类器（classifier）**：将特征映射到类别

总参数数量：约28,501,605个参数

## 详细架构

### 1. 3D CNN模块 (conv2d)

这是一个基于ResNet的3D CNN结构，包含以下部分：

- **初始层**：
  - 3D卷积（3通道输入，64通道输出，1×7×7核）
  - BatchNorm3D和ECA注意力机制

- **ResNet层**：
  - layer1: 2个基本块（64→64通道）
  - layer2: 2个基本块（64→128通道）包含下采样
  - 空间相关模块(corr1)和帧注意力处理器
  - layer3: 2个基本块（128→256通道）包含下采样
  - 空间相关模块(corr2)
  - layer4: 2个基本块（256→512通道）包含下采样
  - 空间相关模块(corr3)和ECA注意力

这个模块处理输入形状为`[批次大小, 3, 帧数, 高度, 宽度]`的数据。

### 2. 1D时序卷积模块 (conv1d)

该模块进一步处理时序信息，结构包括：

- 两层1D卷积层（核大小为5）
- BatchNorm、ReLU和Dropout层
- 输出维度为1024
- 全连接层（1024→1116）

### 3. Transformer模块 (temporal_model)

这是一个包含2层的Transformer结构：

- 每层包含自注意力机制和交叉注意力机制
- 使用维度为512的特征表示
- 包含前馈网络（512→786→512）
- 多个LayerNorm层

### 4. 分类器 (classifier)

- 简单的线性映射层 (1024→1116)
- 输出维度1116，对应手语类别数

## 特点与创新点

1. **混合架构设计**：结合了CNN的局部特征提取能力和Transformer的长序列建模能力

2. **多级时空处理**：
   - 3D CNN捕获短时时空依赖
   - 1D CNN进一步处理时序信息
   - Transformer处理长程依赖关系

3. **注意力增强**：
   - ECA (Efficient Channel Attention)注意力模块
   - 空间相关性模块
   - 帧级注意力处理器

4. **灵活的特征提取**：可以从不同层提取特征用于其他任务

## 使用示例

```python
import torch
from rebuild_model import CompleteModel

# 创建模型实例
model = CompleteModel()

# 加载预训练权重
checkpoint = torch.load('dev_19.48_epoch35_model.pt', map_location='cpu')
if "model_state_dict" in checkpoint:
    # 注意：实际应用可能需要调整架构以匹配权重
    model.load_state_dict(checkpoint["model_state_dict"])

# 设置为评估模式
model.eval()

# 准备输入数据
# [批次大小, 通道数, 帧数, 高度, 宽度]
input_data = torch.randn(1, 3, 8, 112, 112)

# 进行推理
with torch.no_grad():
    output = model(input_data)
```

## 注意事项

1. 本文档中的模型架构是通过分析权重文件重建的，可能与原始训练模型存在细微差异。

2. 重建的模型架构可能需要进一步调整才能与原始权重完全匹配。

3. 模型输入尺寸需要根据实际应用场景调整，典型的输入可能是多帧RGB图像组成的视频片段。

4. 该模型在epoch 35时保存，根据权重文件信息显示。 
(base) bresilin@BresilindeMacBook-Pro 12_26_19.48_cnn_trans_1d % python rebuild_model.py 
模型架构重构完成！

原始模型共有参数数量: 28501605
重构模型共有参数数量: 28489003

注意：这是一个模型架构的重构，可能与原始模型有细微差别。
要使用此模型进行推理或训练，可能需要进一步调整架构细节。

重构的模型架构概览:
CompleteModel(
  (conv2d): CNN3DModel(
    (conv1): Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3), bias=False)
    (bn1): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (eca): ECAModule(
      (conv): Conv1d(1, 1, kernel_size=(3,), stride=(1,), padding=(1,), bias=False)
    )
    (layer1): Sequential(
      (0): BasicBlock(
        (conv1): Conv3d(64, 64, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(64, 64, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (1): BasicBlock(
        (conv1): Conv3d(64, 64, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(64, 64, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (layer2): Sequential(
      (0): BasicBlock(
        (conv1): Conv3d(64, 128, kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(128, 128, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (downsample): Sequential(
          (0): Conv3d(64, 128, kernel_size=(1, 1, 1), stride=(1, 2, 2), bias=False)
          (1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): BasicBlock(
        (conv1): Conv3d(128, 128, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(128, 128, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (corr1): SpatialCorrelationModule(
      (down_conv): Conv3d(128, 8, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (down_conv2): Conv3d(128, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (spatial_aggregation1): Conv3d(1, 8, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation2): Conv3d(1, 8, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation3): Conv3d(1, 8, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (conv_back): Conv3d(8, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
    )
    (frame_attention_processor_2): FrameAttentionProcessor(
      (channel_conv): Conv3d(128, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1))
      (MB): ModuleDict(
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (attn): ModuleDict(
          (q): Linear(in_features=256, out_features=256, bias=True)
          (kv): Linear(in_features=256, out_features=512, bias=True)
          (cor_embed): Linear(in_features=2, out_features=256, bias=True)
          (proj): Linear(in_features=256, out_features=256, bias=True)
          (motion_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (mlp): ModuleDict(
          (fc1): Linear(in_features=256, out_features=1024, bias=True)
          (dwconv): ModuleDict(
            (dwconv): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=1024)
          )
          (fc2): Linear(in_features=1024, out_features=256, bias=True)
        )
      )
      (conv1x1): Conv3d(256, 128, kernel_size=(1, 1, 1), stride=(1, 1, 1))
    )
    (eca2): ECAModule(
      (conv): Conv1d(1, 1, kernel_size=(3,), stride=(1,), padding=(1,), bias=False)
    )
    (layer3): Sequential(
      (0): BasicBlock(
        (conv1): Conv3d(128, 256, kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(256, 256, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (downsample): Sequential(
          (0): Conv3d(128, 256, kernel_size=(1, 1, 1), stride=(1, 2, 2), bias=False)
          (1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): BasicBlock(
        (conv1): Conv3d(256, 256, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(256, 256, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (corr2): SpatialCorrelationModule(
      (down_conv): Conv3d(256, 16, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (down_conv2): Conv3d(256, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (spatial_aggregation1): Conv3d(1, 16, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation2): Conv3d(1, 16, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation3): Conv3d(1, 16, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (conv_back): Conv3d(16, 256, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
    )
    (layer4): Sequential(
      (0): BasicBlock(
        (conv1): Conv3d(256, 512, kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(512, 512, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (downsample): Sequential(
          (0): Conv3d(256, 512, kernel_size=(1, 1, 1), stride=(1, 2, 2), bias=False)
          (1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): BasicBlock(
        (conv1): Conv3d(512, 512, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn1): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv3d(512, 512, kernel_size=(1, 3, 3), stride=(1, 1, 1), padding=(0, 1, 1), bias=False)
        (bn2): BatchNorm3d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (corr3): SpatialCorrelationModule(
      (down_conv): Conv3d(512, 32, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (down_conv2): Conv3d(512, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
      (spatial_aggregation1): Conv3d(1, 32, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation2): Conv3d(1, 32, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (spatial_aggregation3): Conv3d(1, 32, kernel_size=(9, 3, 3), stride=(1, 1, 1))
      (conv_back): Conv3d(32, 512, kernel_size=(1, 1, 1), stride=(1, 1, 1), bias=False)
    )
    (eca4): ECAModule(
      (conv): Conv1d(1, 1, kernel_size=(3,), stride=(1,), padding=(1,), bias=False)
    )
  )
  (conv1d): TemporalConv1D(
    (temporal_conv): Sequential(
      (0): Conv1d(512, 1024, kernel_size=(5,), stride=(1,), padding=(2,))
      (1): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.5, inplace=False)
      (4): Conv1d(1024, 1024, kernel_size=(5,), stride=(1,), padding=(2,))
      (5): BatchNorm1d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.5, inplace=False)
    )
    (fc): Linear(in_features=1024, out_features=1116, bias=True)
  )
  (temporal_model): TemporalTransformer(
    (layers): ModuleList(
      (0-1): 2 x TransformerLayer(
        (self_attn): ModuleDict(
          (k_proj): Linear(in_features=512, out_features=512, bias=True)
          (v_proj): Linear(in_features=512, out_features=512, bias=True)
          (q_proj): Linear(in_features=512, out_features=512, bias=True)
          (out_proj): Linear(in_features=512, out_features=512, bias=True)
        )
        (self_attn_layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (encoder_attn): ModuleDict(
          (k_proj): Linear(in_features=512, out_features=512, bias=True)
          (v_proj): Linear(in_features=512, out_features=512, bias=True)
          (q_proj): Linear(in_features=512, out_features=512, bias=True)
          (out_proj): Linear(in_features=512, out_features=512, bias=True)
        )
        (encoder_attn_layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (fc1): Linear(in_features=512, out_features=786, bias=True)
        (fc2): Linear(in_features=786, out_features=512, bias=True)
        (final_layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      )
    )
    (layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
  )
  (classifier): Classifier()
)