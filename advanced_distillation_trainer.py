import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from slr_network import SLRModel
from student_slr_network import StudentSLRModel


class AdvancedKnowledgeDistillationLoss(nn.Module):
    """高级知识蒸馏损失函数 - 包含特征层面的蒸馏"""
    def __init__(self, alpha=0.5, temperature=4.0, feature_weight=0.1):
        super(AdvancedKnowledgeDistillationLoss, self).__init__()
        self.alpha = alpha
        self.temperature = temperature
        self.feature_weight = feature_weight
        self.ctc_loss = nn.CTCLoss(reduction='mean', zero_infinity=False)
        self.kl_div = nn.KLDivLoss(reduction='batchmean')
        self.mse_loss = nn.MSELoss()
        
        # 特征适配层 - 将学生特征映射到教师特征空间
        self.feature_adapters = nn.ModuleDict({
            'cnn_adapter': nn.Linear(256, 512),  # 3D CNN特征适配
            'conv1d_adapter': nn.Linear(512, 1024),  # 1D Conv特征适配
        })
    
    def forward(self, student_outputs, teacher_outputs, student_features, teacher_features,
                labels, label_lengths, feat_lengths):
        """
        计算高级知识蒸馏损失
        Args:
            student_outputs: 学生模型输出字典
            teacher_outputs: 教师模型输出字典
            student_features: 学生模型中间特征
            teacher_features: 教师模型中间特征
            labels: 真实标签
            label_lengths: 标签长度
            feat_lengths: 特征长度
        """
        total_loss = 0
        loss_dict = {}
        
        # 1. 硬标签损失 (CTC Loss)
        student_logits = student_outputs["sequence_logits"]

        # 确保维度匹配
        batch_size = student_logits.size(1)  # [T, B, C]
        if label_lengths.size(0) != batch_size:
            print(f"警告: label_lengths维度不匹配 {label_lengths.size(0)} vs {batch_size}")
            # 截断或填充到正确的batch_size
            if label_lengths.size(0) > batch_size:
                label_lengths = label_lengths[:batch_size]
            else:
                # 填充到batch_size
                padding = torch.zeros(batch_size - label_lengths.size(0), dtype=label_lengths.dtype, device=label_lengths.device)
                label_lengths = torch.cat([label_lengths, padding])

        # 安全地处理feat_lengths
        if isinstance(feat_lengths, torch.Tensor):
            if feat_lengths.dim() == 0:  # 标量张量
                feat_lengths = feat_lengths.unsqueeze(0)  # 转换为1维张量
            feat_lengths_safe = feat_lengths.cpu().int()
        else:
            feat_lengths_safe = torch.tensor([feat_lengths], dtype=torch.int32)

        # 安全地处理label_lengths
        if label_lengths.dim() == 0:
            label_lengths = label_lengths.unsqueeze(0)

        hard_loss = self.ctc_loss(
            student_logits.log_softmax(-1),
            labels.cpu().int(),
            feat_lengths_safe,
            label_lengths.cpu().int()
        )
        loss_dict['hard_loss'] = hard_loss
        total_loss += self.alpha * hard_loss
        
        # 2. 软标签损失 (KL散度)
        teacher_logits = teacher_outputs["sequence_logits"]
        
        # 确保教师和学生输出的时序长度一致
        min_length = min(student_logits.size(0), teacher_logits.size(0))
        student_logits_aligned = student_logits[:min_length]
        teacher_logits_aligned = teacher_logits[:min_length]
        
        # 温度缩放
        student_soft = F.log_softmax(student_logits_aligned / self.temperature, dim=-1)
        teacher_soft = F.softmax(teacher_logits_aligned / self.temperature, dim=-1)
        
        # 重塑为2D进行KL散度计算
        T, B, C = student_soft.shape
        student_soft_2d = student_soft.view(-1, C)
        teacher_soft_2d = teacher_soft.view(-1, C)
        
        soft_loss = self.kl_div(student_soft_2d, teacher_soft_2d) * (self.temperature ** 2)
        loss_dict['soft_loss'] = soft_loss
        total_loss += (1 - self.alpha) * soft_loss
        
        # 3. 特征层面的蒸馏
        feature_loss = 0
        
        # 3D CNN特征蒸馏
        if 'cnn_features' in student_features and 'cnn_features' in teacher_features:
            student_cnn = student_features['cnn_features']  # [B, 256, T]
            teacher_cnn = teacher_features['cnn_features']  # [B, 512, T]
            
            # 适配学生特征到教师特征空间
            student_cnn_adapted = self.feature_adapters['cnn_adapter'](student_cnn.permute(0, 2, 1))
            student_cnn_adapted = student_cnn_adapted.permute(0, 2, 1)  # [B, 512, T]
            
            # 对齐时序维度
            min_t = min(student_cnn_adapted.size(2), teacher_cnn.size(2))
            student_cnn_aligned = student_cnn_adapted[:, :, :min_t]
            teacher_cnn_aligned = teacher_cnn[:, :, :min_t]
            
            cnn_feature_loss = self.mse_loss(student_cnn_aligned, teacher_cnn_aligned)
            feature_loss += cnn_feature_loss
            loss_dict['cnn_feature_loss'] = cnn_feature_loss
        
        # 1D Conv特征蒸馏
        if 'conv1d_features' in student_features and 'conv1d_features' in teacher_features:
            student_conv1d = student_features['conv1d_features']  # [T, B, 512]
            teacher_conv1d = teacher_features['conv1d_features']  # [T, B, 1024]
            
            # 适配学生特征到教师特征空间
            student_conv1d_adapted = self.feature_adapters['conv1d_adapter'](student_conv1d)  # [T, B, 1024]
            
            # 对齐时序维度
            min_t = min(student_conv1d_adapted.size(0), teacher_conv1d.size(0))
            student_conv1d_aligned = student_conv1d_adapted[:min_t]
            teacher_conv1d_aligned = teacher_conv1d[:min_t]
            
            conv1d_feature_loss = self.mse_loss(student_conv1d_aligned, teacher_conv1d_aligned)
            feature_loss += conv1d_feature_loss
            loss_dict['conv1d_feature_loss'] = conv1d_feature_loss
        
        loss_dict['feature_loss'] = feature_loss
        total_loss += self.feature_weight * feature_loss
        
        loss_dict['total_loss'] = total_loss
        return loss_dict


class AdvancedDistillationTrainer:
    """高级知识蒸馏训练器"""
    def __init__(self, teacher_model_path, student_model_config, device='cuda', 
                 alpha=0.5, temperature=4.0, feature_weight=0.1):
        self.device = device
        self.alpha = alpha
        self.temperature = temperature
        self.feature_weight = feature_weight
        
        # 加载教师模型
        print("正在加载教师模型...")
        self.teacher_model = self._load_teacher_model(teacher_model_path)
        self.teacher_model.eval()
        
        # 创建学生模型
        print("正在创建学生模型...")
        self.student_model = StudentSLRModel(**student_model_config)
        self.student_model.to(device)
        
        # 高级知识蒸馏损失函数
        self.distillation_loss = AdvancedKnowledgeDistillationLoss(
            alpha, temperature, feature_weight
        ).to(device)
        
        # 打印模型信息
        self._print_model_info()
    
    def _load_teacher_model(self, model_path):
        """加载教师模型"""
        teacher_config = {
            'num_classes': 1116,
            'c2d_type': 'resnet18',
            'conv_type': 2,
            'use_bn': True,
            'hidden_size': 1024,
            'weight_norm': True,
            'share_classifier': True,
            'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 25.0},
            'gloss_dict': {str(i): [i] for i in range(1116)}
        }
        
        teacher_model = SLRModel(**teacher_config)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location='cpu')
        if "model_state_dict" in checkpoint:
            teacher_model.load_state_dict(checkpoint["model_state_dict"], strict=False)
        else:
            teacher_model.load_state_dict(checkpoint, strict=False)
        
        teacher_model.to(self.device)
        return teacher_model
    
    def _print_model_info(self):
        """打印模型信息"""
        teacher_params = sum(p.numel() for p in self.teacher_model.parameters())
        student_params = sum(p.numel() for p in self.student_model.parameters())
        distillation_params = sum(p.numel() for p in self.distillation_loss.parameters())
        
        print(f"\n=== 高级蒸馏模型信息 ===")
        print(f"教师模型参数数量: {teacher_params:,}")
        print(f"学生模型参数数量: {student_params:,}")
        print(f"蒸馏适配器参数数量: {distillation_params:,}")
        print(f"参数压缩率: {(1 - student_params/teacher_params)*100:.1f}%")
        print(f"蒸馏参数: alpha={self.alpha}, temperature={self.temperature}, feature_weight={self.feature_weight}")
        print("=" * 60)
    
    def _extract_teacher_features(self, x, len_x):
        """提取教师模型的中间特征"""
        features = {}
        
        # 前向传播并提取特征
        if len(x.shape) == 5:
            batch, temp, channel, height, width = x.shape
            # 3D CNN特征
            framewise = self.teacher_model.conv2d(x.permute(0,2,1,3,4)).view(batch, temp, -1).permute(0,2,1)
            features['cnn_features'] = framewise  # [B, 512, T]
            
            # Transformer特征
            framewise = framewise.permute(0, 2, 1)
            tm_outputs = self.teacher_model.temporal_model(framewise)
            tm_outputs = tm_outputs.permute(0, 2, 1)
            
            # 1D Conv特征
            conv1d_outputs = self.teacher_model.conv1d(tm_outputs, len_x)
            features['conv1d_features'] = conv1d_outputs['visual_feat']  # [T, B, 1024]
        
        return features
    
    def _extract_student_features(self, x, len_x):
        """提取学生模型的中间特征"""
        features = {}
        
        if len(x.shape) == 5:
            # 3D CNN特征
            framewise = self.student_model.conv2d(x)  # [B, 256, T]
            features['cnn_features'] = framewise
            
            # Transformer特征
            framewise = framewise.permute(0, 2, 1)
            tm_outputs = self.student_model.temporal_model(framewise.permute(0, 2, 1))
            
            # 1D Conv特征
            conv1d_outputs = self.student_model.conv1d(tm_outputs, len_x)
            features['conv1d_features'] = conv1d_outputs['visual_feat']  # [T, B, 512]
        
        return features
    
    def train_step(self, batch_data):
        """高级训练步骤"""
        self.student_model.train()

        # 解析批次数据
        videos, labels, label_lengths, video_lengths = batch_data
        videos = videos.to(self.device)
        labels = labels.to(self.device)
        label_lengths = label_lengths.to(self.device)
        video_lengths = video_lengths.to(self.device)

        # 数据加载器返回格式: [B, T, C, H, W]
        # 教师模型期望: [B, T, C, H, W] ✅
        # 学生模型期望: [B, C, T, H, W]

        teacher_videos = videos  # 直接使用，格式已正确
        student_videos = videos.permute(0, 2, 1, 3, 4)  # [B, T, C, H, W] -> [B, C, T, H, W]

        # 教师模型前向传播（无梯度）
        # 确保教师模型在训练模式（禁用解码器）
        self.teacher_model.train()

        with torch.no_grad():
            teacher_outputs = self.teacher_model(teacher_videos, video_lengths, labels, label_lengths)
            teacher_features = self._extract_teacher_features(teacher_videos, video_lengths)

        # 学生模型前向传播
        student_outputs = self.student_model(student_videos, video_lengths, labels, label_lengths)
        student_features = self._extract_student_features(student_videos, video_lengths)

        # 计算高级蒸馏损失
        loss_dict = self.distillation_loss(
            student_outputs, teacher_outputs, student_features, teacher_features,
            labels, label_lengths, student_outputs["feat_len"]
        )

        return loss_dict, student_outputs, teacher_outputs

    def save_student_model(self, save_path, epoch, optimizer_state=None, additional_info=None):
        """保存学生模型"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.student_model.state_dict(),
            'model_config': {
                'num_classes': self.student_model.num_classes,
                'hidden_size': 512,
            },
            'distillation_config': {
                'alpha': self.alpha,
                'temperature': self.temperature,
                'feature_weight': self.feature_weight
            },
            'distillation_adapters': self.distillation_loss.state_dict()
        }

        if optimizer_state is not None:
            checkpoint['optimizer_state_dict'] = optimizer_state

        if additional_info is not None:
            checkpoint.update(additional_info)

        torch.save(checkpoint, save_path)
        print(f"学生模型已保存到: {save_path}")


def test_advanced_distillation():
    """测试高级知识蒸馏"""
    print("测试高级知识蒸馏设置...")
    
    # 学生模型配置
    student_config = {
        'num_classes': 1116,
        'c2d_type': 'student',
        'conv_type': 2,
        'use_bn': True,
        'hidden_size': 512,
        'weight_norm': True,
        'share_classifier': True,
        'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 0.0, 'Dist': 0.0}
    }
    
    # 创建高级训练器
    trainer = AdvancedDistillationTrainer(
        teacher_model_path='./dev_19.48_epoch35_model.pt',
        student_model_config=student_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        alpha=0.5,
        temperature=4.0,
        feature_weight=0.1
    )
    
    # 创建测试数据
    batch_size = 2
    seq_len = 16
    height, width = 224, 224
    num_classes = 1116
    
    videos = torch.randn(batch_size, 3, seq_len, height, width)
    labels = torch.randint(1, num_classes, (batch_size, 10))
    label_lengths = torch.tensor([10, 8])
    video_lengths = torch.tensor([seq_len, seq_len])
    
    batch_data = (videos, labels, label_lengths, video_lengths)
    
    # 测试高级训练步骤
    print("测试高级训练步骤...")
    loss_dict, student_out, teacher_out = trainer.train_step(batch_data)
    
    print(f"总损失: {loss_dict['total_loss']:.4f}")
    print(f"硬标签损失: {loss_dict['hard_loss']:.4f}")
    print(f"软标签损失: {loss_dict['soft_loss']:.4f}")
    print(f"特征损失: {loss_dict['feature_loss']:.4f}")
    
    if 'cnn_feature_loss' in loss_dict:
        print(f"3D CNN特征损失: {loss_dict['cnn_feature_loss']:.4f}")
    if 'conv1d_feature_loss' in loss_dict:
        print(f"1D Conv特征损失: {loss_dict['conv1d_feature_loss']:.4f}")
    
    print("高级知识蒸馏设置测试完成！")


if __name__ == "__main__":
    test_advanced_distillation()
