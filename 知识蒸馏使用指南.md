# 🎓 知识蒸馏使用指南

## 📋 当前状态

您刚才完成的训练是在**模拟数据**上的验证，目的是确认蒸馏框架能正常工作。现在需要在**真实手语数据集**上进行正式的知识蒸馏训练。

## 🎯 正确的训练流程

### 1️⃣ **使用真实数据集进行知识蒸馏训练**

```bash
# 使用新的知识蒸馏训练脚本
python main_distillation.py --config configs/student_distillation.yaml --device 0
```

这个命令会：
- ✅ 加载您的真实手语训练集
- ✅ 使用教师模型进行知识蒸馏
- ✅ 在验证集上评估学生模型性能
- ✅ 自动保存最佳模型

### 2️⃣ **与原始训练的对比**

| 原始训练 | 知识蒸馏训练 |
|---------|-------------|
| `python main.py --config configs/baseline.yaml --device 0` | `python main_distillation.py --config configs/student_distillation.yaml --device 0` |
| 训练教师模型（28.5M参数） | 训练学生模型（8.8M参数） |
| 从头开始训练 | 从教师模型学习 |
| 使用标准损失函数 | 使用蒸馏损失函数 |

## 🔧 **配置文件说明**

### `configs/student_distillation.yaml` 的关键配置：

```yaml
# 模型配置
model_args:
  num_classes: 1116      # 匹配教师模型
  hidden_size: 512       # 学生模型维度（教师模型是1024）

# 知识蒸馏配置
distillation:
  teacher_model_path: './dev_19.48_epoch35_model.pt'
  alpha: 0.5             # 硬标签权重（0.5表示硬软标签各占50%）
  temperature: 4.0       # 蒸馏温度（越高越平滑）
  feature_weight: 0.001  # 特征蒸馏权重

# 训练配置
num_epoch: 50           # 训练轮数
batch_size: 2           # 批次大小
base_lr: 0.001          # 学习率
```

## 📊 **训练过程监控**

训练过程中您会看到：

```
=== Epoch 1/50 ===
Epoch 1, Batch 0/500: Loss=25.4321, Hard=12.3456, Soft=8.7654, Feature=4.3211
训练损失: Total=23.4567, Hard=11.2345, Soft=7.8901, Feature=4.3321
验证损失: 22.1234
学生模型WER: 45.67%
教师模型WER: 42.34%
保存最佳模型: ./work_dir/student_distillation/best_student_model.pt
```

### 损失解释：
- **Total**: 总蒸馏损失
- **Hard**: 硬标签CTC损失（与真实标签的差距）
- **Soft**: 软标签KL散度损失（与教师模型输出的差距）
- **Feature**: 特征蒸馏损失（中间特征的差距）

## 🎯 **预期效果**

### 训练收敛指标：
- **损失下降**: 总损失应该稳定下降
- **WER改善**: 学生模型WER逐渐接近教师模型
- **目标性能**: 学生模型达到教师模型85-95%的性能

### 性能对比示例：
```
教师模型（baseline）: WER = 19.48%
学生模型（蒸馏后）: WER = 21-23%（目标）
性能保持率: 85-90%
参数压缩率: 67.9%
```

## 🚀 **开始训练**

### 立即开始：
```bash
cd /root/lanyun-tmp/CorrNet-main
python main_distillation.py --config configs/student_distillation.yaml --device 0
```

### 自定义参数：
```bash
python main_distillation.py \
    --config configs/student_distillation.yaml \
    --device 0 \
    --alpha 0.6 \
    --temperature 5.0 \
    --feature_weight 0.002
```

## 📁 **输出文件**

训练完成后，您会得到：

```
work_dir/student_distillation/
├── best_student_model.pt          # 最佳学生模型
├── student_epoch_5.pt             # 每5个epoch的检查点
├── student_epoch_10.pt
├── ...
└── student_epoch_50.pt            # 最终模型
```

## 🔍 **验证结果**

训练完成后，您可以：

1. **加载最佳学生模型**进行推理测试
2. **对比教师模型和学生模型**在测试集上的表现
3. **分析性能-效率权衡**

## ⚙️ **超参数调优建议**

如果初次训练效果不理想，可以调整：

| 参数 | 当前值 | 调优建议 |
|------|--------|----------|
| `alpha` | 0.5 | 增加到0.6-0.7（更重视硬标签） |
| `temperature` | 4.0 | 降低到2.0-3.0（更尖锐的软标签） |
| `feature_weight` | 0.001 | 增加到0.005-0.01（更重视特征蒸馏） |
| `base_lr` | 0.001 | 降低到0.0005（更稳定的训练） |

## 🎉 **总结**

现在您可以：
1. ✅ 运行 `python main_distillation.py --config configs/student_distillation.yaml --device 0`
2. ✅ 在真实数据集上进行知识蒸馏训练
3. ✅ 获得验证集上的性能评估
4. ✅ 得到一个高效的轻量级学生模型

这就是您需要的完整知识蒸馏训练流程！🚀
