from typing import Optional, Tuple
import torch
import torch.utils.checkpoint
from torch import nn
import random
from transformers.activations import ACT2FN
from transformers import SwinConfig

import math


class Attention(nn.Module):
    """Multi-headed attention from 'Attention Is All You Need' paper"""

    def __init__(
            self,
            embed_dim: int,
            num_heads: int,
            dropout: float = 0.0,
            bias: bool = True,
    ):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.head_dim = embed_dim // num_heads

        if (self.head_dim * num_heads) != self.embed_dim:
            raise ValueError(
                f"embed_dim must be divisible by num_heads (got `embed_dim`: {self.embed_dim}"
                f" and `num_heads`: {num_heads})."
            )
        self.scaling = self.head_dim ** -0.5

        self.k_proj = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.v_proj = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.q_proj = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.out_proj = nn.Linear(embed_dim, embed_dim, bias=bias)

    def _shape(self, tensor: torch.Tensor, seq_len: int, bsz: int):
        return tensor.view(bsz, seq_len, self.num_heads, self.head_dim).transpose(1, 2).contiguous()

    def forward(
            self,
            hidden_states: torch.Tensor,
            attention_mask: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:
        """Input shape: Batch x Time x Channel"""

        bsz, tgt_len, _ = hidden_states.size()

        # get query proj
        query_states = self.q_proj(hidden_states) * self.scaling

        key_states = self._shape(self.k_proj(hidden_states), -1, bsz)
        value_states = self._shape(self.v_proj(hidden_states), -1, bsz)
        # 多头开始
        proj_shape = (bsz * self.num_heads, -1, self.head_dim)
        query_states = self._shape(query_states, tgt_len, bsz).view(*proj_shape)
        key_states = key_states.reshape(*proj_shape)
        value_states = value_states.reshape(*proj_shape)

        attn_weights = torch.bmm(query_states, key_states.transpose(1, 2))

        attn_weights = nn.functional.softmax(attn_weights, dim=-1)

        attn_probs = nn.functional.dropout(attn_weights, p=self.dropout,
                                           training=self.training)  # 对注意力权重应用 Dropout，以防止过拟合。

        attn_output = torch.bmm(attn_probs, value_states)  # 使用 torch.bmm 将注意力权重与值相乘，得到最终的注意力输出。

        attn_output = attn_output.view(bsz, self.num_heads, tgt_len, self.head_dim)

        attn_output = attn_output.transpose(1, 2)

        attn_output = attn_output.reshape(bsz, tgt_len, self.embed_dim)
        # 多头结束
        # 将注意力输出的形状从 [batch_size * num_heads, seq_len, head_dim] 转换回 [batch_size, seq_len, embed_dim]。

        attn_output = self.out_proj(attn_output)  # 通过 out_proj 投影最终的注意力输出

        return attn_output


class EecoderLayer(nn.Module):
    def __init__(self, config):
        super().__init__()
        self.embed_dim = config.d_model

        self.self_attn = Attention(
            embed_dim=self.embed_dim,  # 768
            num_heads=config.decoder_attention_heads,
            dropout=config.attention_dropout,
        )
        self.dropout = config.dropout
        self.activation_fn = ACT2FN[config.activation_function]
        self.activation_dropout = config.activation_dropout

        self.self_attn_layer_norm = nn.LayerNorm(self.embed_dim)
        self.encoder_attn = Attention(
            self.embed_dim,
            config.decoder_attention_heads,
            dropout=config.attention_dropout,
        )
        self.encoder_attn_layer_norm = nn.LayerNorm(self.embed_dim)
        self.fc1 = nn.Linear(self.embed_dim, config.decoder_ffn_dim)
        self.fc2 = nn.Linear(config.decoder_ffn_dim, self.embed_dim)
        self.final_layer_norm = nn.LayerNorm(self.embed_dim)

    def forward(
            self,
            hidden_states: torch.Tensor,
            attention_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        residual = hidden_states
        # print("residual", residual.shape)
        hidden_states = self.self_attn_layer_norm(hidden_states)  # 做一个layernorm

        # add present self-attn cache to positions 1,2 of present_key_value tuple
        hidden_states = self.self_attn(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
        )
        hidden_states = nn.functional.dropout(hidden_states, p=self.dropout, training=self.training)
        hidden_states = residual + hidden_states

        # Fully Connected
        residual = hidden_states
        hidden_states = self.final_layer_norm(hidden_states)
        hidden_states = self.activation_fn(self.fc1(hidden_states))
        hidden_states = nn.functional.dropout(hidden_states, p=self.activation_dropout, training=self.training)
        hidden_states = self.fc2(hidden_states)
        hidden_states = nn.functional.dropout(hidden_states, p=self.dropout, training=self.training)
        hidden_states = residual + hidden_states
        # print("hidden_states最终", hidden_states.shape)
        outputs = (hidden_states,)

        return outputs


class Encoder(nn.Module):
    def __init__(self, config, embed_tokens: Optional[nn.Embedding] = None):
        super().__init__()
        self.config = config
        self.dropout = config.dropout
        self.layerdrop = config.decoder_layerdrop
        self.padding_idx = 0
        self.max_target_positions = config.max_position_embeddings
        self.embed_scale = math.sqrt(config.d_model) if config.scale_embedding else 1.0
        self.layers = nn.ModuleList([EecoderLayer(config) for _ in range(config.decoder_layers)])
        self.layer_norm = nn.LayerNorm(config.d_model)

    def get_position_embeddings(self, seq_len, d_model, device):
        """
        生成正弦位置编码
        Args:
            seq_len: 序列长度（视频帧数）
            d_model: 特征维度 (768)
            device: 计算设备
        Returns:
            position_embeddings: shape (1, seq_len, d_model)
        """
        position = torch.arange(seq_len, dtype=torch.float, device=device).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, dtype=torch.float, device=device) *
                             (-math.log(10000.0) / d_model))

        pe = torch.zeros(1, seq_len, d_model, device=device)
        pe[0, :, 0::2] = torch.sin(position * div_term)
        pe[0, :, 1::2] = torch.cos(position * div_term)

        return pe

    def forward(
            self,
            input_ids=None,
            attention_mask=None,

    ):
        input_ids.to("cuda:0")
        batch_size, num_frames, _ = input_ids.shape        #print(num_frames)
        position_embeddings = self.get_position_embeddings(
            seq_len=num_frames,
            d_model=512,
            device=input_ids.device
        )
        position_embeddings.to("cuda:0")


        hidden_states = input_ids + position_embeddings  # 词嵌入和位置嵌入相加

        # check if head_mask/cross_attn_head_mask has a correct number of layers specified if desired
        for idx, decoder_layer in enumerate(self.layers):
            # dropout_probability = random.uniform(0, 1)
            # if self.training and (dropout_probability < self.layerdrop):  # self.layerdrop这个必须为0.0
            #     continue

            layer_outputs = decoder_layer(  # 运行这里!!!!!1
                hidden_states,
                attention_mask=attention_mask,
            )
            hidden_states = layer_outputs[0]  # 取元组里的第一个数, 本来也就一个数

        # add final layer norm
        hidden_states = self.layer_norm(hidden_states)

        # print("hidden_states", hidden_states.shape)
        return hidden_states


# SwinConfig.json = SwinConfig.from_json_file("SwinConfig.json")
# model = Encoder(SwinConfig.json=SwinConfig.json)
# input = torch.ones(2, 33, 1024)
# # input_mask = (input.permute(0, 2, 1) != 0).int().permute((0, 2, 1))
# # print(input_mask.shape)
# ouput = model(input_ids=input)
