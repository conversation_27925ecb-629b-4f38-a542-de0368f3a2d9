import torch
import torch.nn as nn
import torch.utils.model_zoo as model_zoo
import torch.nn.functional as F
import math
from timm.models.layers import DropPath, to_2tuple, trunc_normal_


# 修改output_dim 就是你要插入的卷积层的通道数， 还有要调整3D卷积，可以点forward函数
__all__ = [
    'ResNet', 'resnet10', 'resnet18', 'resnet34', 'resnet50', 'resnet101',
    'resnet152', 'resnet200'
]
model_urls = {
    'resnet18': 'https://download.pytorch.org/models/resnet18-f37072fd.pth',
    'resnet34': 'https://download.pytorch.org/models/resnet34-333f7ec4.pth',
    'resnet50': 'https://download.pytorch.org/models/resnet50-19c8e357.pth',
    'resnet101': 'https://download.pytorch.org/models/resnet101-5d3b4d8f.pth',
    'resnet152': 'https://download.pytorch.org/models/resnet152-b121ed2d.pth',
}

class ECA_layer(nn.Module):
    """构建一个 ECA 模块。

    参数:
        channel: 输入特征图的通道数
        k_size: 自适应选择的一维卷积核大小
    """

    def __init__(self, channel, k_size=3):
        super(ECA_layer, self).__init__()

        # 全局平均池化层，用于将每个通道的空间信息压缩成一个值
        self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # 一维卷积层，用于捕捉通道之间的交互信息
        # 1. 输入通道数为1，因为经过全局平均池化后，每个特征图都变成了1x1
        # 2. 输出通道数为1，因为我们不想改变通道数量，只是调整权重
        # 3. kernel_size=k_size，指定卷积核的大小
        # 4. padding=(k_size - 1) // 2，用于保持卷积后的张量长度与输入一致
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)

        # Sigmoid激活函数，将输出的范围限制在(0, 1)之间
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        """
        前向传播函数，定义数据流经过该模块的处理步骤。

        参数:
        x (Tensor): 输入张量，形状为 (batch_size, channels, height, width)。

        返回:
        Tensor: 经过ECA模块处理后的输出张量。
        """

        # 使用全局平均池化将每个通道的空间维度 (H, W) 压缩到 1x1
        # 输出张量的形状将变为 [batch_size, channels, 1, 1]
        y = self.avg_pool(x)

        # 去掉最后一个维度，并交换第二个和第三个维度
        # y.squeeze(-1) 的形状是 [batch_size, channels, 1]
        # y.transpose(-1, -2) 交换后的形状是 [batch_size, 1, channels]
        y = y.squeeze(-1).transpose(-1, -2)

        # 通过一维卷积处理，卷积核大小是 k_size
        # 形状保持 [batch_size, 1, channels]，内容经过一维卷积核处理
        y = self.conv(y)

        # 再次交换维度，恢复原始的通道顺序
        # y.transpose(-1, -2) 将形状从 [batch_size, 1, channels] 变为 [batch_size, channels, 1]
        y = y.transpose(-1, -2)

        # 恢复被去掉的维度，将形状从 [batch_size, channels, 1] 变为 [batch_size, channels, 1, 1]
        y = y.unsqueeze(-1)

        # 使用 Sigmoid 激活函数将输出限制在 (0, 1) 之间
        y = self.sigmoid(y)

        # 将输入张量 x 与处理后的权重 y 相乘，进行通道加权
        # expand_as 确保 y 的形状与 x 匹配，以便逐元素相乘
        return x * y.expand_as(x)


class ECA_5D(nn.Module):
    def __init__(self, channel, k_size=3):
        super(ECA_5D, self).__init__()
        self.eca = ECA_layer(channel, k_size)

    def forward(self, x):
        # x 的形状为 (batch, channel, frame, height, width)
        b, c, t, h, w = x.shape

        # 将帧维度合并到 batch 维度，形状变为 (batch * frame, channel, height, width)
        x = x.permute(0, 2, 1, 3, 4).reshape(b * t, c, h, w)

        # 使用四维 ECA 模块处理
        x = self.eca(x)

        # 恢复到五维形状 (batch, channel, frame, height, width)
        x = x.reshape(b, t, c, h, w).permute(0, 2, 1, 3, 4)
        return x
class DWConv(nn.Module):
    def __init__(self, dim):
        super(DWConv, self).__init__()
        self.dwconv = nn.Conv2d(dim, dim, 3, 1, 1, bias=True, groups=dim)

    def forward(self, x, H, W):
        B, N, C = x.shape
        x = x.transpose(1, 2).reshape(B, C, H, W)
        x = self.dwconv(x)
        x = x.reshape(B, C, -1).transpose(1, 2)

        return x


def window_partition(x, window_size):
    B, H, W, C = x.shape  # B, 224, 224, 3
    x = x.view(B, H // window_size[0], window_size[0], W // window_size[1], window_size[1], C)
    # Batchsize, 224/7=32, 7, 224/7=32, 7, 3   这里已经把window_size给单独分出来了
    windows = (
        x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size[0] * window_size[1], C)
    )  # B, 32, 32, 7, 7, 147 -> B*32*32,49,3
    return windows


# window_reverse 函数的作用是将之前通过 window_partition 操作分割成多个窗口的特征重新组合成原始的图像张量
def window_reverse(windows, window_size, H, W):
    nwB, N, C = windows.shape
    windows = windows.view(-1, window_size[0], window_size[1], C)
    B = int(nwB / (H * W / window_size[0] / window_size[1]))
    x = windows.view(
        B, H // window_size[0], W // window_size[1], window_size[0], window_size[1], -1
    )
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return x


# pad_if_needed 函数的作用是在图像的边界上进行填充，以便将其分割成大小为 window_size 的窗口。如果图像的宽度和高度不能被 window_size 整除，
# 则会在图像的中心进行填充，并返回相应的注意力掩码（attn_mask）
def pad_if_needed(x, size, window_size):
    n, h, w, c = size
    pad_h = math.ceil(h / window_size[0]) * window_size[0] - h
    pad_w = math.ceil(w / window_size[1]) * window_size[1] - w
    if pad_h > 0 or pad_w > 0:  # center-pad the feature on H and W axes
        img_mask = torch.zeros((1, h + pad_h, w + pad_w, 1))  # 1 H W 1
        h_slices = (
            slice(0, pad_h // 2),
            slice(pad_h // 2, h + pad_h // 2),
            slice(h + pad_h // 2, None),
        )
        w_slices = (
            slice(0, pad_w // 2),
            slice(pad_w // 2, w + pad_w // 2),
            slice(w + pad_w // 2, None),
        )
        cnt = 0
        for h in h_slices:
            for w in w_slices:
                img_mask[:, h, w, :] = cnt
                cnt += 1

        mask_windows = window_partition(
            img_mask, window_size
        )  # nW, window_size*window_size, 1
        mask_windows = mask_windows.squeeze(-1)
        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        attn_mask = attn_mask.masked_fill(
            attn_mask != 0, float(-100.0)
        ).masked_fill(attn_mask == 0, float(0.0))
        return nn.functional.pad(
            x,
            (0, 0, pad_w // 2, pad_w - pad_w // 2, pad_h // 2, pad_h - pad_h // 2),
        ), attn_mask
    return x, None


# depad_if_needed 函数的作用是从图像中移除之前在中心进行的填充，以便恢复原始图像。
# 如果图像在高度或宽度上进行了填充，则会从这些维度中移除相应的像素，并返回恢复后的图像。
def depad_if_needed(x, size, window_size):
    n, h, w, c = size
    pad_h = math.ceil(h / window_size[0]) * window_size[0] - h
    pad_w = math.ceil(w / window_size[1]) * window_size[1] - w
    if pad_h > 0 or pad_w > 0:  # remove the center-padding on feature
        return x[:, pad_h // 2: pad_h // 2 + h, pad_w // 2: pad_w // 2 + w, :].contiguous()
    return x


class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.dwconv = DWConv(hidden_features)  # 经过深度可分离卷积中第二阶段的逐点卷积将维度变成了 hidden_features即dim
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)
        self.relu = nn.ReLU(inplace=True)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x, H, W):
        x = self.fc1(x)
        x = self.dwconv(x, H, W)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class InterFrameAttention(nn.Module):
    def __init__(self, dim, motion_dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0.):
        super().__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."

        self.dim = dim
        self.motion_dim = motion_dim

        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.cor_embed = nn.Linear(2, motion_dim, bias=qkv_bias)

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.motion_proj = nn.Linear(motion_dim, motion_dim)

        self.proj_drop = nn.Dropout(proj_drop)
        self.apply(self._init_weights)
        self.sigmoid = nn.Sigmoid()

    # 这段代码是一个初始化权重的函数，用于在模型的所有层中设置初始权重。具体来说，它确保了线性层、归一化层和卷积层的权重和偏置都被正确初始化
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x1, x2, cor, H, W, mask=None):  # cor是坐标图
        B, N, C = x1.shape
        B, N, C_c = cor.shape

        q = self.q(x1).reshape(B, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)
        kv = self.kv(x2).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        cor_embed_ = self.cor_embed(cor)
        cor_embed = cor_embed_.reshape(B, N, self.num_heads, self.motion_dim // self.num_heads).permute(0, 2, 1, 3)

        k, v = kv[0], kv[1]
        attn = (q @ k.transpose(-2, -1)) * self.scale

        if mask is not None:
            nW = mask.shape[0]  # mask: nW, N, N
            attn = attn.view(B // nW, nW, self.num_heads, N, N) + mask.unsqueeze(
                1
            ).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = attn.softmax(dim=-1)
        else:
            attn = attn.softmax(dim=-1)

        attn = self.attn_drop(attn)
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        c_reverse = (attn @ cor_embed).transpose(1, 2).reshape(B, N, -1)
        motion = self.motion_proj(c_reverse - cor_embed_)

        x = self.proj(x)
        x = self.proj_drop(x)
        x = self.sigmoid(x)
        return x, motion


class MotionFormerBlock(nn.Module):
    def __init__(self, dim, motion_dim, num_heads, window_size=0, shift_size=0, mlp_ratio=4., bidirectional=True,
                 qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, ):
        super().__init__()
        self.window_size = window_size
        if not isinstance(self.window_size, (tuple, list)):
            self.window_size = to_2tuple(window_size)
        self.shift_size = shift_size
        if not isinstance(self.shift_size, (tuple, list)):
            self.shift_size = to_2tuple(shift_size)
        self.bidirectional = bidirectional
        self.norm1 = norm_layer(dim)
        self.attn = InterFrameAttention(
            dim,
            motion_dim,
            num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale,
            attn_drop=attn_drop, proj_drop=drop)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x, cor, H, W, B):
        # print("进入MotionFormerBlock之前的x的形状: ", x.shape)
        x = x.view(2 * B, H, W, -1)
        x_new = x
        x_new = x_new.view(2 * B, H * W, -1)

        # print("x_new的shape:", x_new.shape)
        x_pad, mask = pad_if_needed(x, x.size(), self.window_size)
        cor_pad, _ = pad_if_needed(cor, cor.size(), self.window_size)

        if self.shift_size[0] or self.shift_size[1]:
            _, H_p, W_p, C = x_pad.shape
            x_pad = torch.roll(x_pad, shifts=(-self.shift_size[0], -self.shift_size[1]), dims=(1, 2))
            cor_pad = torch.roll(cor_pad, shifts=(-self.shift_size[0], -self.shift_size[1]), dims=(1, 2))

            if hasattr(self, 'HW') and self.HW.item() == H_p * W_p:
                shift_mask = self.attn_mask
            else:
                shift_mask = torch.zeros((1, H_p, W_p, 1))  # 1 H W 1
                h_slices = (slice(0, -self.window_size[0]),
                            slice(-self.window_size[0], -self.shift_size[0]),
                            slice(-self.shift_size[0], None))
                w_slices = (slice(0, -self.window_size[1]),
                            slice(-self.window_size[1], -self.shift_size[1]),
                            slice(-self.shift_size[1], None))
                cnt = 0
                for h in h_slices:
                    for w in w_slices:
                        shift_mask[:, h, w, :] = cnt
                        cnt += 1

                mask_windows = window_partition(shift_mask, self.window_size).squeeze(-1)
                shift_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
                shift_mask = shift_mask.masked_fill(shift_mask != 0,
                                                    float(-100.0)).masked_fill(shift_mask == 0,
                                                                               float(0.0))

                if mask is not None:
                    shift_mask = shift_mask.masked_fill(mask != 0,
                                                        float(-100.0))
                self.register_buffer("attn_mask", shift_mask)
                self.register_buffer("HW", torch.Tensor([H_p * W_p]))
        else:
            shift_mask = mask

        if shift_mask is not None:
            shift_mask = shift_mask.to(x_pad.device)

        _, Hw, Ww, C = x_pad.shape
        x_win = window_partition(x_pad, self.window_size)
        cor_win = window_partition(cor_pad, self.window_size)

        nwB = x_win.shape[0]
        x_norm = self.norm1(x_win)

        x_reverse = torch.cat([x_norm[nwB // 2:], x_norm[:nwB // 2]])
        # print("经过帧间注意力之前的x.shape:", x_norm.shape, x_reverse.shape)
        x_appearence, x_motion = self.attn(x_norm, x_reverse, cor_win, H, W, shift_mask)
        x_norm = x_norm + self.drop_path(x_appearence)

        x_back = x_norm
        x_back_win = window_reverse(x_back, self.window_size, Hw, Ww)
        x_motion = window_reverse(x_motion, self.window_size, Hw, Ww)

        if self.shift_size[0] or self.shift_size[1]:
            x_back_win = torch.roll(x_back_win, shifts=(self.shift_size[0], self.shift_size[1]), dims=(1, 2))
            x_motion = torch.roll(x_motion, shifts=(self.shift_size[0], self.shift_size[1]), dims=(1, 2))

        x = depad_if_needed(x_back_win, x.size(), self.window_size).view(2 * B, H * W, -1)
        x_motion = depad_if_needed(x_motion, cor.size(), self.window_size).view(2 * B, H * W, -1)
        x = x + self.drop_path(self.mlp(self.norm2(x), H, W))
        x_bnew = x_new + x_motion
        # print("!!!!!!!!!经过帧间注意力模块之后的x和x_motion的形状: ", x.shape, x_motion.shape)
        return x_bnew, x_motion


# 定义帧间注意力处理类
class FrameAttentionProcessor(nn.Module):
    def __init__(self, dim=256, motion_dim=256, num_heads=8, window_size=5, shift_size=2, output_channels=128):
        super(FrameAttentionProcessor, self).__init__()
        self.dim = dim
        self.motion_dim = motion_dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.output_channels = output_channels
        self.H, self.W = 28, 28  # 假设帧的高度和宽度
        self.B = 2  # 假设 batch size
        self.channel_conv = nn.Conv3d(
            in_channels=128,
            out_channels=256,
            kernel_size=1,
            stride=1,
            padding=0
        )
        self.cor = {}

        # 实例化 MotionFormerBlock
        self.MB = MotionFormerBlock(
            dim=self.dim,
            motion_dim=self.motion_dim,
            num_heads=self.num_heads,
            window_size=self.window_size,
            shift_size=self.shift_size,
            mlp_ratio=4,
            qkv_bias=True,
            qk_scale=None,
            drop=0.,
            attn_drop=0.,
            drop_path=0.,
            norm_layer=nn.LayerNorm
        )

        # 1x1 卷积用于通道降维
        self.conv1x1 = nn.Conv3d(in_channels=self.dim, out_channels=self.output_channels, kernel_size=(1, 1, 1))

    def get_cor(self, shape, device):
        k = (str(shape), str(device))
        if k not in self.cor:
            tenHorizontal = torch.linspace(-1.0, 1.0, shape[2], device=device).view(
                1, 1, 1, shape[2]).expand(shape[0], -1, shape[1], -1).permute(0, 2, 3, 1)
            tenVertical = torch.linspace(-1.0, 1.0, shape[1], device=device).view(
                1, 1, shape[1], 1).expand(shape[0], -1, -1, shape[2]).permute(0, 2, 3, 1)
            self.cor[k] = torch.cat([tenHorizontal, tenVertical], -1).to(device)
        return self.cor[k]

    def forward(self, input_tensor):
        time_outputs = []
        input_tensor = self.channel_conv(input_tensor)
        # 遍历每个时间帧对，执行帧间注意力
        for t in range(input_tensor.size(2) - 1):  # 总共有 N-1 对帧
            x1 = input_tensor[:, :, t, :, :]  # t 时刻的当前帧
            x2 = input_tensor[:, :, t + 1, :, :]  # t+1 时刻的下一帧
            x = torch.cat([x1, x2], 0)  # 合并帧对为 [4, 256, 28, 28]
            cor = self.get_cor((4, 28, 28), "cuda:0")
            # 执行帧间注意力
            x, x_motion = self.MB(x, cor, self.H, self.W, self.B)
            # print("x_motion的shape:", x_motion.shape)
            # print("x的shape:", x.shape)
            # 还原形状为 [4, 256, 28, 28]
            x = x.view(self.B * 2, self.dim, self.H, self.W)

            # 只保留 x1 的部分，取前两个样本 [2, 256, 28, 28]
            x1_output = x[:self.B]
            time_outputs.append(x1_output)

        # 将最后一帧添加到 time_outputs
        last_frame = input_tensor[:, :, -1, :, :]  # 取出最后一帧 [2, 256, 28, 28]
        time_outputs.append(last_frame)

        # 在时间维度上拼接
        time_concat = torch.stack(time_outputs, dim=2)  # 形状 [2, 256, 时间帧数, 28, 28]

        # 执行通道降维
        output = self.conv1x1(time_concat)

        return output  # 最终输出形状 [2, 64, 时间帧数, 28, 28]


class Get_Correlation(nn.Module):
    def __init__(self, channels):
        super().__init__()
        reduction_channel = channels // 16
        self.down_conv = nn.Conv3d(channels, reduction_channel, kernel_size=1, bias=False)

        self.down_conv2 = nn.Conv3d(channels, channels, kernel_size=1, bias=False)
        self.spatial_aggregation1 = nn.Conv3d(reduction_channel, reduction_channel, kernel_size=(9, 3, 3),
                                              padding=(4, 1, 1), groups=reduction_channel)
        self.spatial_aggregation2 = nn.Conv3d(reduction_channel, reduction_channel, kernel_size=(9, 3, 3),
                                              padding=(4, 2, 2), dilation=(1, 2, 2), groups=reduction_channel)
        self.spatial_aggregation3 = nn.Conv3d(reduction_channel, reduction_channel, kernel_size=(9, 3, 3),
                                              padding=(4, 3, 3), dilation=(1, 3, 3), groups=reduction_channel)
        self.weights = nn.Parameter(torch.ones(3) / 3, requires_grad=True)
        self.weights2 = nn.Parameter(torch.ones(2) / 2, requires_grad=True)
        self.conv_back = nn.Conv3d(reduction_channel, channels, kernel_size=1, bias=False)

    def forward(self, x):
        x2 = self.down_conv2(x)
        affinities = torch.einsum('bcthw,bctsd->bthwsd', x,
                                  torch.concat([x2[:, :, 1:], x2[:, :, -1:]], 2))  # repeat the last frame
        affinities2 = torch.einsum('bcthw,bctsd->bthwsd', x,
                                   torch.concat([x2[:, :, :1], x2[:, :, :-1]], 2))  # repeat the first frame
        features = torch.einsum('bctsd,bthwsd->bcthw', torch.concat([x2[:, :, 1:], x2[:, :, -1:]], 2),
                                F.sigmoid(affinities) - 0.5) * self.weights2[0] + \
                   torch.einsum('bctsd,bthwsd->bcthw', torch.concat([x2[:, :, :1], x2[:, :, :-1]], 2),
                                F.sigmoid(affinities2) - 0.5) * self.weights2[1]

        x = self.down_conv(x)
        aggregated_x = self.spatial_aggregation1(x) * self.weights[0] + self.spatial_aggregation2(x) * self.weights[1] \
                       + self.spatial_aggregation3(x) * self.weights[2]
        aggregated_x = self.conv_back(aggregated_x)

        return features * (F.sigmoid(aggregated_x) - 0.5)


def conv3x3(in_planes, out_planes, stride=1):
    # 3x3x3 convolution with padding
    return nn.Conv3d(
        in_planes,
        out_planes,
        kernel_size=(1, 3, 3),
        stride=(1, stride, stride),
        padding=(0, 1, 1),
        bias=False)


class BasicBlock(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(BasicBlock, self).__init__()
        self.conv1 = conv3x3(inplanes, planes, stride)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = conv3x3(planes, planes)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)
        # print("经过resnet之后的形状：", out.shape)
        return out


class ResNet(nn.Module):

    def __init__(self, block, layers, num_classes=1000):
        self.inplanes = 64
        super(ResNet, self).__init__()
        self.conv1 = nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3),
                               bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1))
        self.eca = ECA_5D(64)
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.corr1 = Get_Correlation(self.inplanes)
        self.frame_attention_processor_2 = FrameAttentionProcessor()
        self.eca2 = ECA_5D(128)

        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.corr2 = Get_Correlation(self.inplanes)
        self.alpha = nn.Parameter(torch.zeros(3), requires_grad=True)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2)
        self.corr3 = Get_Correlation(self.inplanes)
        self.eca4 = ECA_5D(512)

        self.avgpool = nn.AvgPool2d(7, stride=1)
        self.fc = nn.Linear(512 * block.expansion, num_classes)
        self.drop1 = nn.Dropout(0.15)
        self.drop2 = nn.Dropout(0.2)
        self.drop3 = nn.Dropout(0.3)
        self.drop4 = nn.Dropout(0.4)

        for m in self.modules():
            if isinstance(m, nn.Conv3d) or isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv3d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=(1, stride, stride), bias=False),
                nn.BatchNorm3d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        N, C, T, H, W = x.size()
        # print("最初的x的形状：", x.shape)
        x = self.conv1(x)
        # print("3d卷积之后的形状：", x.shape)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        # print("池化之后的形状：", x.shape)
        x = self.eca(x)
        x = self.layer1(x)
        # print("经过layer1之后的形状：", x.shape)
        x = self.layer2(x)
        # print("经过layer2之后的形状：", x.shape)
        x = x + self.corr1(x) * self.alpha[0]
        x = self.eca2(x)
        x = self.drop2(x)
        #x = self.frame_attention_processor_2(x)
        x = self.layer3(x)
        # print("经过layer3之后的形状：", x.shape)
        x = x + self.corr2(x) * self.alpha[1]
        x = self.drop2(x)
        x = self.layer4(x)
        # print("经过layer4之后的形状：", x.shape)
        x = x + self.corr3(x) * self.alpha[2]
        x = self.drop2(x)
        x = x.transpose(1, 2).contiguous()
        # print("transpose之后的形状：", x.shape)
        x = x.view((-1,) + x.size()[2:])  # bt,c,h,w
        # print("view之后的形状：", x.shape)
        x = self.avgpool(x)
        # print("avgpool之后的shape：", x.shape)
        x = x.view(x.size(0), -1)  # bt,c
        # print("view之后的shape：", x.shape)
        x = self.fc(x)  # bt,c
        # print("经过resnet之后的形状：", x.shape)
        return x


def resnet18(**kwargs):
    """Constructs a ResNet-18 based model.
    """
    model = ResNet(BasicBlock, [2, 2, 2, 2], **kwargs)
    checkpoint = model_zoo.load_url(model_urls['resnet18'])
    layer_name = list(checkpoint.keys())
    for ln in layer_name:
        if 'conv' in ln or 'downsample.0.weight' in ln:
            checkpoint[ln] = checkpoint[ln].unsqueeze(2)
    model.load_state_dict(checkpoint, strict=False)
    return model


def resnet34(**kwargs):
    """Constructs a ResNet-34 model.
    """
    model = ResNet(BasicBlock, [3, 4, 6, 3], **kwargs)
    return model


# def test():
#     net = resnet18()
#     y = net(torch.randn(2, 3, 200, 224, 224))
#     # print(y.size())


# test()