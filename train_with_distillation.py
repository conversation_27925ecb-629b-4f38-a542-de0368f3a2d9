#!/usr/bin/env python3
"""
使用知识蒸馏训练学生模型的完整训练脚本
"""

import os
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import StepLR
import numpy as np
from advanced_distillation_trainer import AdvancedDistillationTrainer
import argparse
import yaml
from dataset.dataloader_video import BaseFeeder


def create_data_loader(config, mode='train'):
    """创建数据加载器"""
    feeder_args = config['feeder_args'].copy()
    feeder_args['mode'] = mode
    
    # 动态导入feeder类
    feeder_class = config['feeder']
    module_name, class_name = feeder_class.rsplit('.', 1)
    module = __import__(module_name, fromlist=[class_name])
    feeder_cls = getattr(module, class_name)
    
    # 创建数据集
    dataset = feeder_cls(**feeder_args)
    
    # 创建数据加载器
    batch_size = config['batch_size'] if mode == 'train' else config['test_batch_size']
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=config['num_worker'],
        drop_last=(mode == 'train'),
        pin_memory=True
    )
    
    return data_loader


def train_epoch(trainer, data_loader, optimizer, epoch, device, log_interval=100):
    """训练一个epoch"""
    trainer.student_model.train()
    total_loss = 0
    total_hard_loss = 0
    total_soft_loss = 0
    total_feature_loss = 0
    
    for batch_idx, batch_data in enumerate(data_loader):
        # 解析数据
        if len(batch_data) == 4:
            videos, labels, label_lengths, video_lengths = batch_data
        else:
            # 处理不同的数据格式
            videos = batch_data[0]
            labels = batch_data[1] if len(batch_data) > 1 else None
            label_lengths = batch_data[2] if len(batch_data) > 2 else None
            video_lengths = batch_data[3] if len(batch_data) > 3 else torch.tensor([videos.size(2)] * videos.size(0))
        
        # 移动到设备
        videos = videos.to(device)
        if labels is not None:
            labels = labels.to(device)
        if label_lengths is not None:
            label_lengths = label_lengths.to(device)
        video_lengths = video_lengths.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        
        try:
            loss_dict, student_out, teacher_out = trainer.train_step(
                (videos, labels, label_lengths, video_lengths)
            )
            
            # 反向传播
            loss_dict['total_loss'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(trainer.student_model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 累计损失
            total_loss += loss_dict['total_loss'].item()
            total_hard_loss += loss_dict['hard_loss'].item()
            total_soft_loss += loss_dict['soft_loss'].item()
            total_feature_loss += loss_dict['feature_loss'].item()
            
            # 打印日志
            if batch_idx % log_interval == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}/{len(data_loader)}: '
                      f'Loss={loss_dict["total_loss"].item():.4f}, '
                      f'Hard={loss_dict["hard_loss"].item():.4f}, '
                      f'Soft={loss_dict["soft_loss"].item():.4f}, '
                      f'Feature={loss_dict["feature_loss"].item():.4f}')
                
        except Exception as e:
            print(f"训练批次 {batch_idx} 出错: {e}")
            continue
    
    # 返回平均损失
    num_batches = len(data_loader)
    return {
        'total_loss': total_loss / num_batches,
        'hard_loss': total_hard_loss / num_batches,
        'soft_loss': total_soft_loss / num_batches,
        'feature_loss': total_feature_loss / num_batches
    }


def validate_epoch(trainer, data_loader, device):
    """验证一个epoch"""
    trainer.student_model.eval()
    total_loss = 0
    total_hard_loss = 0
    total_soft_loss = 0
    total_feature_loss = 0
    
    with torch.no_grad():
        for batch_idx, batch_data in enumerate(data_loader):
            try:
                # 解析数据
                if len(batch_data) == 4:
                    videos, labels, label_lengths, video_lengths = batch_data
                else:
                    videos = batch_data[0]
                    labels = batch_data[1] if len(batch_data) > 1 else None
                    label_lengths = batch_data[2] if len(batch_data) > 2 else None
                    video_lengths = batch_data[3] if len(batch_data) > 3 else torch.tensor([videos.size(2)] * videos.size(0))
                
                # 移动到设备
                videos = videos.to(device)
                if labels is not None:
                    labels = labels.to(device)
                if label_lengths is not None:
                    label_lengths = label_lengths.to(device)
                video_lengths = video_lengths.to(device)
                
                # 验证步骤
                loss_dict, student_out, teacher_out = trainer.train_step(
                    (videos, labels, label_lengths, video_lengths)
                )
                
                # 累计损失
                total_loss += loss_dict['total_loss'].item()
                total_hard_loss += loss_dict['hard_loss'].item()
                total_soft_loss += loss_dict['soft_loss'].item()
                total_feature_loss += loss_dict['feature_loss'].item()
                
            except Exception as e:
                print(f"验证批次 {batch_idx} 出错: {e}")
                continue
    
    # 返回平均损失
    num_batches = len(data_loader)
    return {
        'total_loss': total_loss / num_batches,
        'hard_loss': total_hard_loss / num_batches,
        'soft_loss': total_soft_loss / num_batches,
        'feature_loss': total_feature_loss / num_batches
    }


def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='知识蒸馏训练')
    parser.add_argument('--config', default='configs/student_distillation.yaml', help='配置文件路径')
    parser.add_argument('--teacher_model', default='dev_19.48_epoch35_model.pt', help='教师模型路径')
    parser.add_argument('--device', default='0', help='GPU设备')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--alpha', type=float, default=0.5, help='硬标签权重')
    parser.add_argument('--temperature', type=float, default=4.0, help='蒸馏温度')
    parser.add_argument('--feature_weight', type=float, default=0.01, help='特征蒸馏权重')
    
    args = parser.parse_args()
    
    # 设置设备
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 学生模型配置
    student_config = {
        'num_classes': 1116,
        'c2d_type': 'student',
        'conv_type': 2,
        'use_bn': True,
        'hidden_size': 512,
        'weight_norm': True,
        'share_classifier': True,
        'loss_weights': {'SeqCTC': 1.0, 'ConvCTC': 0.0, 'Dist': 0.0}
    }
    
    # 创建训练器
    print("创建知识蒸馏训练器...")
    trainer = AdvancedDistillationTrainer(
        teacher_model_path=args.teacher_model,
        student_model_config=student_config,
        device=device,
        alpha=args.alpha,
        temperature=args.temperature,
        feature_weight=args.feature_weight
    )
    
    # 创建优化器
    optimizer = optim.AdamW(
        trainer.student_model.parameters(),
        lr=args.lr,
        weight_decay=0.001
    )
    
    # 学习率调度器
    scheduler = StepLR(optimizer, step_size=5, gamma=0.5)
    
    # 创建简单的测试数据（如果没有真实数据集）
    print("创建测试数据...")
    batch_size = 2
    seq_len = 16
    height, width = 224, 224
    num_classes = 1116
    
    # 模拟训练数据
    train_data = []
    for _ in range(20):  # 20个批次的模拟数据
        videos = torch.randn(batch_size, 3, seq_len, height, width)
        labels = torch.randint(1, num_classes, (batch_size, 10))
        label_lengths = torch.tensor([10, 8])
        video_lengths = torch.tensor([seq_len, seq_len])
        train_data.append((videos, labels, label_lengths, video_lengths))
    
    # 开始训练
    print(f"开始训练 {args.epochs} 个epoch...")
    best_loss = float('inf')
    
    for epoch in range(args.epochs):
        print(f"\n=== Epoch {epoch+1}/{args.epochs} ===")
        
        # 训练
        trainer.student_model.train()
        epoch_loss = 0
        for batch_idx, batch_data in enumerate(train_data):
            optimizer.zero_grad()
            
            try:
                loss_dict, student_out, teacher_out = trainer.train_step(batch_data)
                loss_dict['total_loss'].backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(trainer.student_model.parameters(), max_norm=1.0)
                
                optimizer.step()
                epoch_loss += loss_dict['total_loss'].item()
                
                if batch_idx % 5 == 0:
                    print(f'Batch {batch_idx}: Loss={loss_dict["total_loss"].item():.4f}')
                    
            except Exception as e:
                print(f"训练批次 {batch_idx} 出错: {e}")
                continue
        
        avg_loss = epoch_loss / len(train_data)
        print(f'Epoch {epoch+1} 平均损失: {avg_loss:.4f}')
        
        # 更新学习率
        scheduler.step()
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            save_path = f'student_model_epoch_{epoch+1}_loss_{avg_loss:.4f}.pt'
            trainer.save_student_model(save_path, epoch+1, optimizer.state_dict())
            print(f'保存最佳模型: {save_path}')
    
    print(f"\n训练完成！最佳损失: {best_loss:.4f}")


if __name__ == "__main__":
    main()
