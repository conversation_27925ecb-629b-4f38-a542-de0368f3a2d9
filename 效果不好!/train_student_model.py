#!/usr/bin/env python3
"""
学生模型知识蒸馏训练脚本
基于原main.py框架，专门用于训练学生模型

使用方法:
python train_student_model.py --config configs/student_distillation.yaml --device 0
"""

import os
import sys
import yaml
import torch
import shutil
import argparse

# 添加必要的路径
sys.path.append('.')
sys.path.append('12_26_19.48_cnn_trans_1d')

# 导入原框架组件
import utils
from main import Processor, import_class
from seq_scripts import seq_train, seq_eval

class StudentDistillationProcessor(Processor):
    """
    继承原Processor，专门用于学生模型知识蒸馏训练
    """
    
    def __init__(self, arg):
        # 确保使用学生模型
        if 'student' not in arg.model.lower():
            print(f"⚠️ Warning: Model '{arg.model}' doesn't seem to be a student model")
            print("Make sure you're using the correct model configuration")
        
        # 调用父类初始化
        super(StudentDistillationProcessor, self).__init__(arg)
        
        # 验证教师模型路径
        teacher_path = getattr(arg.model_args, 'teacher_model_path', None)
        if teacher_path and not os.path.exists(teacher_path):
            print(f"❌ Teacher model not found: {teacher_path}")
            print("Please check the teacher_model_path in your config")
            sys.exit(1)
        elif teacher_path:
            print(f"✅ Teacher model found: {teacher_path}")
        else:
            print("⚠️ No teacher model specified, using random teacher for testing")
    
    def start(self):
        """重写start方法，添加知识蒸馏特定的训练逻辑"""
        if self.arg.phase == 'train':
            print("🚀 Starting Knowledge Distillation Training...")
            print(f"📊 Student Model: {self.arg.model}")
            print(f"👨‍🏫 Teacher Model: {getattr(self.arg.model_args, 'teacher_model_path', 'Random')}")
            print(f"🔥 Distillation Alpha: {getattr(self.arg.model_args, 'distillation_alpha', 0.5)}")
            print(f"🌡️ Temperature: {getattr(self.arg.model_args, 'distillation_temperature', 4.0)}")
            
            # 统计模型参数
            student_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            print(f"📈 Student Model Parameters: {student_params:,}")
            
            best_dev = 100.0
            best_epoch = 0
            total_time = 0
            
            self.recoder.print_log('Parameters:\n{}\n'.format(str(vars(self.arg))))
            
            for epoch in range(self.arg.optimizer_args['start_epoch'], self.arg.num_epoch):
                save_model = epoch % self.arg.save_interval == 0
                eval_model = epoch % self.arg.eval_interval == 0
                
                import time
                epoch_time = time.time()
                
                # 知识蒸馏训练
                print(f"\n📚 Epoch {epoch+1}/{self.arg.num_epoch} - Knowledge Distillation Training")
                seq_train(self.data_loader['train'], self.model, self.optimizer,
                          self.device, epoch, self.recoder)
                
                # 验证
                if eval_model:
                    print(f"🔍 Evaluating student model...")
                    dev_wer = seq_eval(self.arg, self.data_loader['dev'], self.model, self.device,
                                       'dev', epoch, self.arg.work_dir, self.recoder, self.arg.evaluate_tool)
                    self.recoder.print_log("📊 Student Dev WER: {:05.2f}%".format(dev_wer))
                
                # 保存最佳模型
                if dev_wer < best_dev:
                    best_dev = dev_wer
                    best_epoch = epoch
                    model_path = f"{self.arg.work_dir}student_best_model.pt"
                    self.save_model(epoch, model_path)
                    self.recoder.print_log('💾 Save best student model')
                
                self.recoder.print_log('🏆 Best_dev: {:05.2f}, Epoch : {}'.format(best_dev, best_epoch))
                
                # 定期保存
                if save_model:
                    model_path = f"{self.arg.work_dir}student_dev_{dev_wer:05.2f}_epoch{epoch}_model.pt"
                    self.save_model(epoch, model_path)
                
                epoch_time = time.time() - epoch_time
                total_time += epoch_time
                torch.cuda.empty_cache()
                
                self.recoder.print_log('⏱️ Epoch {} costs {} mins {} seconds'.format(
                    epoch, int(epoch_time)//60, int(epoch_time)%60))
            
            self.recoder.print_log('🎉 Knowledge Distillation Training completed!')
            self.recoder.print_log('⏱️ Total training time: {} hours {} mins {} seconds'.format(
                int(total_time)//60//60, int(total_time)//60%60, int(total_time)%60))
            self.recoder.print_log(f'🏆 Best student model WER: {best_dev:.2f}% at epoch {best_epoch}')
            
        elif self.arg.phase == 'test':
            print("🧪 Testing Student Model...")
            if self.arg.load_weights is None and self.arg.load_checkpoints is None:
                print('❌ Please specify --load_weights or --load_checkpoints for testing.')
                return
            
            self.recoder.print_log('🎯 Student Model: {}.'.format(self.arg.model))
            self.recoder.print_log('📁 Weights: {}.'.format(self.arg.load_weights))
            
            # 测试学生模型
            dev_wer = seq_eval(self.arg, self.data_loader["dev"], self.model, self.device,
                               "dev", 6667, self.arg.work_dir, self.recoder, self.arg.evaluate_tool)
            test_wer = seq_eval(self.arg, self.data_loader["test"], self.model, self.device,
                                "test", 6667, self.arg.work_dir, self.recoder, self.arg.evaluate_tool)
            
            self.recoder.print_log('📊 Student Model Results:')
            self.recoder.print_log(f'  Dev WER: {dev_wer:.2f}%')
            self.recoder.print_log(f'  Test WER: {test_wer:.2f}%')
            self.recoder.print_log('✅ Evaluation Done.\n')

def main():
    """主函数"""
    # 解析命令行参数
    parser = utils.get_parser()
    args = parser.parse_args()
    
    # 加载配置文件
    if args.config is not None:
        with open(args.config, 'r') as f:
            try:
                default_arg = yaml.load(f, Loader=yaml.FullLoader)
            except AttributeError:
                default_arg = yaml.load(f)
        
        # 验证配置参数
        key = vars(args).keys()
        for k in default_arg.keys():
            if k not in key:
                print('❌ WRONG ARG: {}'.format(k))
                assert (k in key)
        
        parser.set_defaults(**default_arg)
    
    args = parser.parse_args()
    
    # 加载数据集信息
    with open(f"./configs/{args.dataset}.yaml", 'r') as f:
        args.dataset_info = yaml.load(f, Loader=yaml.FullLoader)
    
    # 打印配置信息
    print("🔧 Knowledge Distillation Configuration:")
    print(f"  📁 Config: {args.config}")
    print(f"  🗂️ Dataset: {args.dataset}")
    print(f"  🎯 Model: {args.model}")
    print(f"  📊 Batch Size: {args.batch_size}")
    print(f"  🔄 Epochs: {args.num_epoch}")
    print(f"  💾 Work Dir: {args.work_dir}")
    print(f"  🖥️ Device: {args.device}")
    
    # 创建处理器并开始训练
    processor = StudentDistillationProcessor(args)
    
    # 打包代码（用于实验记录）
    utils.pack_code("./", args.work_dir)
    
    # 开始训练
    processor.start()

if __name__ == '__main__':
    main()
