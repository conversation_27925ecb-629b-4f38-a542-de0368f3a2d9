# 学生模型知识蒸馏训练指南

## 概述

本指南详细说明如何使用设计的轻量级学生模型进行知识蒸馏训练，从预训练的教师模型 `dev_19.48_epoch35_model.pt` 中学习知识。

## 模型架构对比

### 教师模型 (CompleteModel)
- **总参数量**: ~28,501,605 (约28.5M)
- **架构**: 3D CNN (ResNet-based) + 1D Conv + Transformer
- **特征维度**: 512 → 1024 → 1116
- **Transformer**: 2层，8头注意力

### 学生模型 (StudentSignLanguageModel)
- **总参数量**: ~3,500,000 (约3.5M，压缩率87%)
- **架构**: 轻量级3D CNN + 深度可分离1D Conv + 轻量级Transformer
- **特征维度**: 256 → 512 → 1116
- **Transformer**: 2层，4头注意力

## 核心设计特点

### 1. 轻量级3D CNN (StudentCNN3D)
- 使用深度可分离卷积替代标准卷积
- 通道数减少50%：32→64→128→256 (vs 教师模型的64→128→256→512)
- 保留ECA注意力机制和空间相关性模块
- 参数压缩率：~75%

### 2. 深度可分离1D卷积 (LightweightTemporalConv1D)
- 深度卷积 + 逐点卷积的组合
- 大幅减少参数量同时保持时序建模能力
- 参数压缩率：~80%

### 3. 轻量级Transformer (LightweightTransformer)
- 注意力头数：4头 (vs 教师模型8头)
- 前馈网络维度：512 (vs 教师模型786)
- 保持2层结构以维持长序列建模能力
- 参数压缩率：~60%

## 使用方法

### 1. 环境准备

```bash
# 确保安装必要的依赖
pip install torch torchvision tqdm numpy
```

### 2. 快速开始

```python
import torch
from student_model import StudentSignLanguageModel
from knowledge_distillation_trainer import KnowledgeDistillationTrainer

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 创建学生模型
student_model = StudentSignLanguageModel(
    num_classes=1116,  # 手语类别数
    hidden_dim=256     # 隐藏维度
)

# 创建知识蒸馏训练器
trainer = KnowledgeDistillationTrainer(
    teacher_model_path="12_26_19.48_cnn_trans_1d/dev_19.48_epoch35_model.pt",
    student_model=student_model,
    device=device,
    alpha=0.5,          # 硬标签权重
    temperature=4.0,    # 蒸馏温度
    feature_weight=0.1  # 特征蒸馏权重
)

# 开始训练（需要准备数据加载器）
# best_accuracy = trainer.train(train_loader, val_loader, num_epochs=100)
```

### 3. 数据加载器准备

您需要准备符合以下格式的数据加载器：

```python
# 数据格式
# 输入: [batch_size, 3, frames, height, width]
# 标签: [batch_size] (长整型)

# 示例数据加载器创建
from torch.utils.data import DataLoader, Dataset

class SignLanguageDataset(Dataset):
    def __init__(self, video_paths, labels, transform=None):
        self.video_paths = video_paths
        self.labels = labels
        self.transform = transform
    
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        # 加载视频数据，形状应为 [3, frames, height, width]
        video = self.load_video(self.video_paths[idx])
        label = self.labels[idx]
        
        if self.transform:
            video = self.transform(video)
        
        return video, label
    
    def load_video(self, path):
        # 实现视频加载逻辑
        # 返回形状为 [3, frames, height, width] 的tensor
        pass

# 创建数据加载器
train_dataset = SignLanguageDataset(train_video_paths, train_labels)
val_dataset = SignLanguageDataset(val_video_paths, val_labels)

train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False)
```

### 4. 完整训练脚本

```python
import torch
from torch.utils.data import DataLoader
from student_model import StudentSignLanguageModel
from knowledge_distillation_trainer import KnowledgeDistillationTrainer

def main():
    # 配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    num_classes = 1116
    hidden_dim = 256
    batch_size = 16
    num_epochs = 100
    
    print(f"使用设备: {device}")
    
    # 创建学生模型
    student_model = StudentSignLanguageModel(
        num_classes=num_classes,
        hidden_dim=hidden_dim
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in student_model.parameters())
    print(f"学生模型参数数量: {total_params:,}")
    
    # 准备数据加载器（您需要实现这部分）
    # train_loader = create_train_loader(batch_size)
    # val_loader = create_val_loader(batch_size)
    
    # 创建训练器
    trainer = KnowledgeDistillationTrainer(
        teacher_model_path="12_26_19.48_cnn_trans_1d/dev_19.48_epoch35_model.pt",
        student_model=student_model,
        device=device,
        alpha=0.5,
        temperature=4.0,
        feature_weight=0.1
    )
    
    # 开始训练
    # best_accuracy = trainer.train(
    #     train_loader=train_loader,
    #     val_loader=val_loader,
    #     num_epochs=num_epochs,
    #     save_path='student_model_best.pth'
    # )
    
    print("训练器准备完成！请添加您的数据加载器后开始训练。")

if __name__ == "__main__":
    main()
```

## 超参数调优建议

### 1. 蒸馏参数
- **alpha (硬标签权重)**: 0.3-0.7，建议从0.5开始
- **temperature (温度参数)**: 3.0-6.0，建议从4.0开始
- **feature_weight (特征蒸馏权重)**: 0.05-0.2，建议从0.1开始

### 2. 训练参数
- **学习率**: 0.001 (初始)，每30个epoch衰减0.1
- **批次大小**: 16-32 (根据GPU内存调整)
- **训练轮数**: 100-200

### 3. 模型参数
- **hidden_dim**: 192-320，建议256
- **Transformer层数**: 2-3层
- **注意力头数**: 4-6头

## 模型评估

### 1. 加载训练好的模型

```python
# 加载最佳模型
checkpoint = torch.load('student_model_best.pth')
student_model.load_state_dict(checkpoint['model_state_dict'])
student_model.eval()

print(f"最佳模型准确率: {checkpoint['accuracy']:.2f}%")
```

### 2. 性能对比

```python
def compare_models(teacher_model, student_model, test_loader, device):
    """对比教师模型和学生模型的性能"""
    
    def evaluate_model(model, loader):
        model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, targets in loader:
                data, targets = data.to(device), targets.to(device)
                outputs = model(data)
                _, predicted = torch.max(outputs.data, 1)
                total += targets.size(0)
                correct += (predicted == targets).sum().item()
        
        return 100 * correct / total
    
    teacher_acc = evaluate_model(teacher_model, test_loader)
    student_acc = evaluate_model(student_model, test_loader)
    
    print(f"教师模型准确率: {teacher_acc:.2f}%")
    print(f"学生模型准确率: {student_acc:.2f}%")
    print(f"性能保持率: {student_acc/teacher_acc*100:.1f}%")
```

## 注意事项

1. **内存管理**: 学生模型虽然轻量，但训练时需要同时加载教师模型，注意GPU内存使用
2. **数据预处理**: 确保输入数据格式与教师模型训练时一致
3. **收敛监控**: 关注软标签损失的收敛情况，这是蒸馏效果的重要指标
4. **特征蒸馏**: 当前实现中特征蒸馏是可选的，可以根据效果决定是否启用

## 预期效果

- **模型大小**: 压缩至原模型的13% (~3.5M vs ~28.5M)
- **推理速度**: 提升3-5倍
- **准确率保持**: 预期保持教师模型85-95%的性能
- **内存占用**: 减少约80%

## 快速测试

运行以下命令测试模型是否正常工作：

```bash
python test_models.py
```

通过合理的超参数调优和充分的训练，学生模型应该能够在大幅减少参数量的同时保持良好的性能表现。
