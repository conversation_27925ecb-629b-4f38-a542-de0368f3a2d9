#!/usr/bin/env python3
"""
修复导入问题的脚本
"""

import os
import sys
import shutil

def fix_imports():
    """修复导入问题"""
    print("🔧 Fixing import issues...")
    
    # 1. 检查文件是否存在
    files_to_check = [
        "12_26_19.48_cnn_trans_1d/complete_model.py",
        "student_model.py",
        "student_slr_network.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
    
    # 2. 复制complete_model.py到当前目录
    source = "12_26_19.48_cnn_trans_1d/complete_model.py"
    target = "complete_model.py"
    
    if os.path.exists(source):
        shutil.copy2(source, target)
        print(f"✅ Copied {source} to {target}")
    else:
        print(f"❌ Source file not found: {source}")
        return False
    
    # 3. 检查Python路径
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"🐍 Python path: {sys.path}")
    
    return True

def test_imports():
    """测试导入"""
    print("\n🧪 Testing imports...")
    
    try:
        # 测试基本导入
        import torch
        print("✅ torch imported")
        
        import torch.nn as nn
        print("✅ torch.nn imported")
        
        # 测试学生模型导入
        from student_model import StudentSignLanguageModel
        print("✅ StudentSignLanguageModel imported")
        
        # 测试教师模型导入
        from complete_model import CompleteModel
        print("✅ CompleteModel imported")
        
        # 测试集成模型导入
        from student_slr_network import StudentSLRModel
        print("✅ StudentSLRModel imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test():
    """创建简单测试"""
    print("\n🧪 Creating simple test...")
    
    try:
        from student_slr_network import StudentSLRModel
        
        # 创建模型（不加载教师模型权重）
        model = StudentSLRModel(
            num_classes=1116,
            teacher_model_path=None,  # 不加载教师模型
            loss_weights={'SeqCTC': 1.0, 'ConvCTC': 1.0, 'Dist': 10.0}
        )
        
        print(f"✅ Model created successfully")
        print(f"📊 Parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 简单前向传播测试
        batch_size = 1
        seq_len = 32
        vid = torch.randn(batch_size, seq_len, 3, 224, 224)
        vid_lgt = torch.tensor([seq_len])
        
        model.eval()
        with torch.no_grad():
            ret_dict = model(vid, vid_lgt)
        
        print(f"✅ Forward pass successful")
        print(f"📤 Output keys: {list(ret_dict.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Simple test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting import fix...")
    
    # 修复导入
    if fix_imports():
        print("✅ Import fix completed")
    else:
        print("❌ Import fix failed")
        sys.exit(1)
    
    # 测试导入
    if test_imports():
        print("✅ Import test passed")
    else:
        print("❌ Import test failed")
        sys.exit(1)
    
    # 简单测试
    if create_simple_test():
        print("✅ Simple test passed")
        print("\n🎉 All fixes successful! You can now run the training.")
    else:
        print("❌ Simple test failed")
        sys.exit(1)
