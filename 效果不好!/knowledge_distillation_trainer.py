import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import os
import sys

# 导入模型
from student_model import StudentSignLanguageModel

# 尝试导入CompleteModel，如果失败则创建一个简化版本
try:
    sys.path.append('12_26_19.48_cnn_trans_1d')
    from complete_model import CompleteModel
except ImportError:
    # 创建一个简化的CompleteModel用于知识蒸馏
    class CompleteModel(nn.Module):
        def __init__(self):
            super().__init__()
            # 创建一个简化的教师模型架构，用于加载权重
            # 这个架构应该与实际的教师模型权重兼容
            from slr_network import SLRModel

            # 创建一个临时的gloss_dict来避免错误
            temp_gloss_dict = {f'gloss_{i}': [i] for i in range(1115)}

            self.model = SLRModel(
                num_classes=1116,
                c2d_type='resnet18',
                conv_type=2,
                use_bn=True,
                hidden_size=1024,
                gloss_dict=temp_gloss_dict,
                weight_norm=True,
                share_classifier=True
            )

        def forward(self, x):
            # 简化的前向传播，只返回logits
            try:
                outputs = self.model(x, None)
                return outputs.get('sequence_logits', outputs.get('conv_logits', torch.randn(x.shape[0], 1116, device=x.device)))
            except:
                # 如果出错，返回随机输出
                batch_size = x.shape[0]
                return torch.randn(batch_size, 1116, device=x.device)

        def load_state_dict(self, state_dict, strict=True):
            # 尝试加载权重，如果失败就忽略
            try:
                return self.model.load_state_dict(state_dict, strict=False)
            except:
                print("Warning: Could not load teacher model weights, using random initialization")
                return None

class KnowledgeDistillationLoss(nn.Module):
    """知识蒸馏损失函数"""
    def __init__(self, alpha=0.5, temperature=4.0, feature_weight=0.1):
        super(KnowledgeDistillationLoss, self).__init__()
        self.alpha = alpha  # 硬标签和软标签损失的平衡因子
        self.temperature = temperature  # 温度参数
        self.feature_weight = feature_weight  # 特征蒸馏权重
        self.ce_loss = nn.CrossEntropyLoss()
        self.mse_loss = nn.MSELoss()
        
    def forward(self, student_logits, teacher_logits, targets, 
                student_features=None, teacher_features=None):
        """
        计算知识蒸馏损失
        
        Args:
            student_logits: 学生模型的输出 [B, num_classes]
            teacher_logits: 教师模型的输出 [B, num_classes]
            targets: 真实标签 [B]
            student_features: 学生模型的特征 (可选)
            teacher_features: 教师模型的特征 (可选)
        """
        
        # 1. 硬标签损失（交叉熵损失）
        hard_loss = self.ce_loss(student_logits, targets)
        
        # 2. 软标签损失（KL散度损失）
        soft_student = F.log_softmax(student_logits / self.temperature, dim=1)
        soft_teacher = F.softmax(teacher_logits / self.temperature, dim=1)
        soft_loss = F.kl_div(soft_student, soft_teacher, reduction='batchmean') * (self.temperature ** 2)
        
        # 3. 总的分类损失
        total_loss = self.alpha * hard_loss + (1 - self.alpha) * soft_loss
        
        # 4. 特征蒸馏损失（如果提供了特征）
        feature_loss = 0
        if student_features is not None and teacher_features is not None:
            # 确保特征维度匹配
            if student_features.shape != teacher_features.shape:
                # 如果维度不匹配，使用线性层进行适配
                if hasattr(self, 'feature_adapter'):
                    student_features = self.feature_adapter(student_features)
                else:
                    # 简单的维度调整
                    if student_features.size(-1) != teacher_features.size(-1):
                        min_dim = min(student_features.size(-1), teacher_features.size(-1))
                        student_features = student_features[..., :min_dim]
                        teacher_features = teacher_features[..., :min_dim]
            
            feature_loss = self.mse_loss(student_features, teacher_features)
            total_loss += self.feature_weight * feature_loss
        
        return total_loss, hard_loss, soft_loss, feature_loss

class KnowledgeDistillationTrainer:
    """知识蒸馏训练器"""
    
    def __init__(self, teacher_model_path, student_model, device='cuda', 
                 alpha=0.5, temperature=4.0, feature_weight=0.1):
        self.device = device
        
        # 加载教师模型
        print("正在加载教师模型...")
        self.teacher_model = CompleteModel()
        checkpoint = torch.load(teacher_model_path, map_location=device)
        
        if "model_state_dict" in checkpoint:
            self.teacher_model.load_state_dict(checkpoint["model_state_dict"])
            print("成功加载教师模型权重！")
        else:
            print("警告：未找到model_state_dict，尝试直接加载...")
            self.teacher_model.load_state_dict(checkpoint)
        
        self.teacher_model.to(device)
        self.teacher_model.eval()
        
        # 冻结教师模型参数
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # 学生模型
        self.student_model = student_model.to(device)
        
        # 损失函数
        self.criterion = KnowledgeDistillationLoss(alpha, temperature, feature_weight)
        
        # 优化器
        self.optimizer = optim.Adam(self.student_model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=30, gamma=0.1)
        
        # 训练历史
        self.train_losses = []
        self.val_accuracies = []
        
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.student_model.train()
        total_loss = 0
        total_hard_loss = 0
        total_soft_loss = 0
        total_feature_loss = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, (data, targets) in enumerate(pbar):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 教师模型前向传播
            with torch.no_grad():
                teacher_logits = self.teacher_model(data)
            
            # 学生模型前向传播
            student_logits, student_features = self.student_model(data, return_features=True)
            
            # 计算损失
            loss, hard_loss, soft_loss, feature_loss = self.criterion(
                student_logits, teacher_logits, targets,
                student_features['fused_features'], None  # 暂时不使用特征蒸馏
            )
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            total_hard_loss += hard_loss.item()
            total_soft_loss += soft_loss.item()
            if isinstance(feature_loss, torch.Tensor):
                total_feature_loss += feature_loss.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Hard': f'{hard_loss.item():.4f}',
                'Soft': f'{soft_loss.item():.4f}'
            })
        
        avg_loss = total_loss / len(train_loader)
        avg_hard_loss = total_hard_loss / len(train_loader)
        avg_soft_loss = total_soft_loss / len(train_loader)
        avg_feature_loss = total_feature_loss / len(train_loader)
        
        return avg_loss, avg_hard_loss, avg_soft_loss, avg_feature_loss
    
    def validate(self, val_loader):
        """验证模型"""
        self.student_model.eval()
        correct = 0
        total = 0
        val_loss = 0
        
        with torch.no_grad():
            for data, targets in tqdm(val_loader, desc="Validation"):
                data, targets = data.to(self.device), targets.to(self.device)
                
                # 学生模型预测
                student_logits = self.student_model(data)
                
                # 教师模型预测（用于计算验证损失）
                teacher_logits = self.teacher_model(data)
                
                # 计算损失
                loss, _, _, _ = self.criterion(student_logits, teacher_logits, targets)
                val_loss += loss.item()
                
                # 计算准确率
                _, predicted = torch.max(student_logits.data, 1)
                total += targets.size(0)
                correct += (predicted == targets).sum().item()
        
        accuracy = 100 * correct / total
        avg_val_loss = val_loss / len(val_loader)
        
        return accuracy, avg_val_loss
    
    def train(self, train_loader, val_loader, num_epochs=100, save_path='student_model_best.pth'):
        """完整的训练流程"""
        best_accuracy = 0
        
        print(f"开始知识蒸馏训练，共 {num_epochs} 个epoch")
        print(f"教师模型参数数量: {sum(p.numel() for p in self.teacher_model.parameters()):,}")
        print(f"学生模型参数数量: {sum(p.numel() for p in self.student_model.parameters()):,}")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss, hard_loss, soft_loss, feature_loss = self.train_epoch(train_loader)
            
            # 验证
            val_accuracy, val_loss = self.validate(val_loader)
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_accuracies.append(val_accuracy)
            
            # 打印结果
            print(f"训练损失: {train_loss:.4f} (硬: {hard_loss:.4f}, 软: {soft_loss:.4f}, 特征: {feature_loss:.4f})")
            print(f"验证损失: {val_loss:.4f}, 验证准确率: {val_accuracy:.2f}%")
            print(f"当前学习率: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 保存最佳模型
            if val_accuracy > best_accuracy:
                best_accuracy = val_accuracy
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.student_model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'accuracy': val_accuracy,
                    'loss': train_loss
                }, save_path)
                print(f"保存最佳模型，准确率: {val_accuracy:.2f}%")
        
        print(f"\n训练完成！最佳验证准确率: {best_accuracy:.2f}%")
        return best_accuracy

def load_teacher_model(model_path, device='cuda'):
    """加载教师模型的辅助函数"""
    teacher_model = CompleteModel()
    checkpoint = torch.load(model_path, map_location=device)
    
    if "model_state_dict" in checkpoint:
        teacher_model.load_state_dict(checkpoint["model_state_dict"])
    else:
        teacher_model.load_state_dict(checkpoint)
    
    teacher_model.to(device)
    teacher_model.eval()
    
    return teacher_model

def create_student_model(num_classes=1116, hidden_dim=256):
    """创建学生模型的辅助函数"""
    return StudentSignLanguageModel(num_classes=num_classes, hidden_dim=hidden_dim)

if __name__ == "__main__":
    # 示例使用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建学生模型
    student_model = create_student_model(num_classes=1116, hidden_dim=256)
    
    # 教师模型路径
    teacher_model_path = "dev_19.48_epoch35_model.pt"
    
    # 创建训练器
    trainer = KnowledgeDistillationTrainer(
        teacher_model_path=teacher_model_path,
        student_model=student_model,
        device=device,
        alpha=0.5,  # 硬标签权重
        temperature=4.0,  # 温度参数
        feature_weight=0.1  # 特征蒸馏权重
    )
    
    print("知识蒸馏训练器创建完成！")
    print("请准备您的数据加载器，然后调用 trainer.train(train_loader, val_loader) 开始训练")
