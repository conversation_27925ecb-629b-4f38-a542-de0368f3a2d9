import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import mobilenet_v3_small

class LightweightECAModule(nn.Module):
    """轻量级ECA注意力模块"""
    def __init__(self, kernel_size=3):
        super(LightweightECAModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=kernel_size, padding=(kernel_size - 1) // 2, bias=False)

    def forward(self, x):
        # 特征图的形状: [B, C, T, H, W]
        y = self.avg_pool(x)  # [B, C, 1, 1, 1]
        y = y.squeeze(-1).squeeze(-1).squeeze(-1)  # [B, C]
        y = y.unsqueeze(-1).transpose(-1, -2)  # [B, 1, C]
        y = self.conv(y)  # [B, 1, C]
        y = y.transpose(-1, -2)  # [B, C, 1]

        # 正确地扩展维度以匹配输入
        y = y.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1, 1]
        return x * y

class DepthwiseSeparableConv3D(nn.Module):
    """深度可分离3D卷积"""
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0):
        super(DepthwiseSeparableConv3D, self).__init__()
        self.depthwise = nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=False)
        self.pointwise = nn.Conv3d(in_channels, out_channels, 1, bias=False)
        self.bn1 = nn.BatchNorm3d(in_channels)
        self.bn2 = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=False)
        
    def forward(self, x):
        x = self.depthwise(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.pointwise(x)
        x = self.bn2(x)
        x = self.relu(x)
        return x

class LightweightBasicBlock(nn.Module):
    """轻量级ResNet基本块"""
    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(LightweightBasicBlock, self).__init__()
        # 使用深度可分离卷积替代标准卷积
        self.conv1 = DepthwiseSeparableConv3D(inplanes, planes, kernel_size=(1, 3, 3), stride=stride, padding=(0, 1, 1))
        self.conv2 = DepthwiseSeparableConv3D(planes, planes, kernel_size=(1, 3, 3), padding=(0, 1, 1))
        self.downsample = downsample
        
    def forward(self, x):
        identity = x
        out = self.conv1(x)
        out = self.conv2(out)
        
        if self.downsample is not None:
            identity = self.downsample(x)
        
        out = out + identity
        return out

class LightweightSpatialCorrelationModule(nn.Module):
    """轻量级空间相关性模块"""
    def __init__(self, in_channels, reduction_ratio=8):
        super(LightweightSpatialCorrelationModule, self).__init__()
        reduced_channels = max(in_channels // reduction_ratio, 8)
        
        self.down_conv = nn.Conv3d(in_channels, reduced_channels, kernel_size=1, bias=False)
        self.spatial_conv = nn.Conv3d(reduced_channels, reduced_channels, kernel_size=(1, 3, 3), padding=(0, 1, 1), bias=False)
        self.up_conv = nn.Conv3d(reduced_channels, in_channels, kernel_size=1, bias=False)
        self.bn = nn.BatchNorm3d(in_channels)
        
    def forward(self, x):
        identity = x
        out = self.down_conv(x)
        out = self.spatial_conv(out)
        out = self.up_conv(out)
        out = self.bn(out)
        return identity + out * 0.1  # 残差连接

class StudentCNN3D(nn.Module):
    """学生模型的轻量级3D CNN"""
    def __init__(self, output_channels=256):
        super(StudentCNN3D, self).__init__()
        
        # 初始卷积层 - 减少通道数
        self.conv1 = nn.Conv3d(3, 32, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3), bias=False)
        self.bn1 = nn.BatchNorm3d(32)
        self.relu = nn.ReLU(inplace=False)
        self.eca1 = LightweightECAModule()
        
        # 轻量级ResNet层
        self.layer1 = nn.Sequential(
            LightweightBasicBlock(32, 32),
            LightweightBasicBlock(32, 32)
        )
        
        self.layer2 = nn.Sequential(
            LightweightBasicBlock(32, 64, stride=(1, 2, 2), downsample=nn.Sequential(
                nn.Conv3d(32, 64, kernel_size=1, stride=(1, 2, 2), bias=False),
                nn.BatchNorm3d(64)
            )),
            LightweightBasicBlock(64, 64)
        )
        
        self.corr1 = LightweightSpatialCorrelationModule(64)
        
        self.layer3 = nn.Sequential(
            LightweightBasicBlock(64, 128, stride=(1, 2, 2), downsample=nn.Sequential(
                nn.Conv3d(64, 128, kernel_size=1, stride=(1, 2, 2), bias=False),
                nn.BatchNorm3d(128)
            )),
            LightweightBasicBlock(128, 128)
        )
        
        self.corr2 = LightweightSpatialCorrelationModule(128)
        
        self.layer4 = nn.Sequential(
            LightweightBasicBlock(128, output_channels, stride=(1, 2, 2), downsample=nn.Sequential(
                nn.Conv3d(128, output_channels, kernel_size=1, stride=(1, 2, 2), bias=False),
                nn.BatchNorm3d(output_channels)
            )),
            LightweightBasicBlock(output_channels, output_channels)
        )
        
        self.eca2 = LightweightECAModule()
        self.avg_pool = nn.AdaptiveAvgPool3d((None, 1, 1))
        
    def forward(self, x):
        # 输入形状: [B, 3, T, H, W]
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.eca1(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.corr1(x)
        
        x = self.layer3(x)
        x = self.corr2(x)
        
        x = self.layer4(x)
        x = self.eca2(x)
        
        # 空间池化，保留时间维度
        x = self.avg_pool(x)  # [B, output_channels, T, 1, 1]
        x = x.squeeze(-1).squeeze(-1)  # [B, output_channels, T]
        
        return x

class LightweightTemporalConv1D(nn.Module):
    """轻量级1D时序卷积模块"""
    def __init__(self, in_channels=256, hidden_channels=512):
        super(LightweightTemporalConv1D, self).__init__()

        # 为了兼容原框架，添加kernel_size属性
        # 格式需要与原框架的TemporalConv一致：['K5', 'K5']表示两个kernel_size=5的卷积层
        self.kernel_size = ['K5', 'K5']  # 两层卷积的kernel size

        # 深度可分离1D卷积
        self.temporal_conv = nn.Sequential(
            # 第一层深度可分离卷积
            nn.Conv1d(in_channels, in_channels, kernel_size=5, padding=2, groups=in_channels, bias=False),
            nn.Conv1d(in_channels, hidden_channels, kernel_size=1, bias=False),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(inplace=False),
            nn.Dropout(0.3),

            # 第二层深度可分离卷积
            nn.Conv1d(hidden_channels, hidden_channels, kernel_size=5, padding=2, groups=hidden_channels, bias=False),
            nn.Conv1d(hidden_channels, hidden_channels, kernel_size=1, bias=False),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(inplace=False),
            nn.Dropout(0.3)
        )

    def forward(self, x):
        # 输入形状: [B, in_channels, T]
        return self.temporal_conv(x)  # [B, hidden_channels, T]

class LightweightTransformerLayer(nn.Module):
    """轻量级Transformer层"""
    def __init__(self, d_model=256, nhead=4, dim_feedforward=512, dropout=0.1):
        super(LightweightTransformerLayer, self).__init__()
        
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.norm1 = nn.LayerNorm(d_model)
        
        # 轻量级前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            nn.ReLU(inplace=False),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, d_model),
            nn.Dropout(dropout)
        )
        self.norm2 = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # 自注意力
        attn_out, _ = self.self_attn(x, x, x)
        x = self.norm1(x + attn_out)
        
        # 前馈网络
        ffn_out = self.ffn(x)
        x = self.norm2(x + ffn_out)
        
        return x

class LightweightTransformer(nn.Module):
    """轻量级Transformer模块"""
    def __init__(self, d_model=256, num_layers=2, nhead=4, dim_feedforward=512):
        super(LightweightTransformer, self).__init__()
        
        self.layers = nn.ModuleList([
            LightweightTransformerLayer(d_model, nhead, dim_feedforward)
            for _ in range(num_layers)
        ])
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # 输入形状: [B, T, d_model]
        for layer in self.layers:
            x = layer(x)
        
        return self.layer_norm(x)

class SimplifiedStudentModel(nn.Module):
    """简化但正确的学生模型 - 确保时序对齐正确"""
    def __init__(self, num_classes=1116, hidden_dim=512):
        super(SimplifiedStudentModel, self).__init__()

        # 轻量级3D CNN - 保持时序维度
        self.conv3d_backbone = nn.Sequential(
            # 第一层：只在空间维度下采样
            nn.Conv3d(3, 64, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3), bias=False),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=False),

            # 残差块1
            LightweightBasicBlock(64, 64),
            LightweightBasicBlock(64, 64),

            # 残差块2 - 空间下采样
            LightweightBasicBlock(64, 128, stride=(1, 2, 2), downsample=nn.Sequential(
                nn.Conv3d(64, 128, kernel_size=1, stride=(1, 2, 2), bias=False),
                nn.BatchNorm3d(128)
            )),
            LightweightBasicBlock(128, 128),

            # 残差块3 - 空间下采样
            LightweightBasicBlock(128, 256, stride=(1, 2, 2), downsample=nn.Sequential(
                nn.Conv3d(128, 256, kernel_size=1, stride=(1, 2, 2), bias=False),
                nn.BatchNorm3d(256)
            )),
            LightweightBasicBlock(256, 256),

            # 全局空间池化 - 只池化空间维度，保持时序维度
            nn.AdaptiveAvgPool3d((None, 1, 1))  # [B, 256, T, 1, 1]
        )

        # 1D时序建模 - 参考教师模型的TemporalConv
        self.temporal_conv = nn.Sequential(
            nn.Conv1d(256, hidden_dim, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=False),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=False)
        )

        # 轻量级Transformer - 简化版本
        self.transformer = LightweightTransformer(
            d_model=hidden_dim,
            num_layers=2,  # 适中的层数
            nhead=8,       # 足够的注意力头
            dim_feedforward=hidden_dim * 2
        )

        # 简单的分类器
        self.classifier = nn.Linear(hidden_dim, num_classes)
        
    def forward(self, x, return_features=False):
        """
        简化但正确的前向传播 - 确保时序维度正确处理
        输入: [B, 3, T, H, W]
        输出: [B, T, num_classes] - 用于CTC
        """
        batch_size, channels, T, H, W = x.shape

        # 调试信息
        if not self.training:
            print(f"Input shape: {x.shape}")

        # 3D CNN特征提取 - 保持时序维度T
        conv3d_out = self.conv3d_backbone(x)  # [B, 256, T, 1, 1]
        conv3d_features = conv3d_out.squeeze(-1).squeeze(-1)  # [B, 256, T]

        if not self.training:
            print(f"After 3D CNN: {conv3d_features.shape}")

        # 1D时序建模
        temporal_features = self.temporal_conv(conv3d_features)  # [B, hidden_dim, T]

        if not self.training:
            print(f"After temporal conv: {temporal_features.shape}")

        # 为Transformer准备输入: [B, hidden_dim, T] -> [B, T, hidden_dim]
        transformer_input = temporal_features.permute(0, 2, 1)  # [B, T, hidden_dim]

        # Transformer处理
        transformer_output = self.transformer(transformer_input)  # [B, T, hidden_dim]

        if not self.training:
            print(f"After transformer: {transformer_output.shape}")

        # 直接分类 - 每个时间步独立分类
        logits = self.classifier(transformer_output)  # [B, T, num_classes]

        if not self.training:
            print(f"Final logits: {logits.shape}")

        if return_features:
            return logits, {
                'conv3d_features': conv3d_features,
                'temporal_features': temporal_features,
                'transformer_features': transformer_output,
                'final_features': transformer_output.mean(dim=1)  # [B, hidden_dim]
            }

        return logits

class MultiScaleFeatureFusion(nn.Module):
    """多尺度特征融合模块"""
    def __init__(self, in_channels, out_channels):
        super(MultiScaleFeatureFusion, self).__init__()

        # 不同尺度的时序卷积
        self.scale1 = nn.Conv1d(in_channels, out_channels//4, kernel_size=1)
        self.scale2 = nn.Conv1d(in_channels, out_channels//4, kernel_size=3, padding=1)
        self.scale3 = nn.Conv1d(in_channels, out_channels//4, kernel_size=5, padding=2)
        self.scale4 = nn.Conv1d(in_channels, out_channels//4, kernel_size=7, padding=3)

        self.fusion = nn.Conv1d(out_channels, out_channels, kernel_size=1)
        self.bn = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU(inplace=False)

    def forward(self, x):
        # x: [B, C, T]
        s1 = self.scale1(x)
        s2 = self.scale2(x)
        s3 = self.scale3(x)
        s4 = self.scale4(x)

        fused = torch.cat([s1, s2, s3, s4], dim=1)
        fused = self.fusion(fused)
        fused = self.bn(fused)
        return self.relu(fused)

class AttentionFeatureFusion(nn.Module):
    """基于注意力的特征融合模块"""
    def __init__(self, conv_dim, trans_dim, output_dim):
        super(AttentionFeatureFusion, self).__init__()

        # 特征投影
        self.conv_proj = nn.Linear(conv_dim, output_dim)
        self.trans_proj = nn.Linear(trans_dim, output_dim)

        # 注意力权重计算
        self.attention = nn.Sequential(
            nn.Linear(output_dim * 2, output_dim),
            nn.ReLU(inplace=False),
            nn.Linear(output_dim, 2),
            nn.Softmax(dim=-1)
        )

        self.layer_norm = nn.LayerNorm(output_dim)

    def forward(self, conv_features, trans_features):
        # conv_features: [B, T, conv_dim]
        # trans_features: [B, T, trans_dim]

        # 特征投影
        conv_proj = self.conv_proj(conv_features)  # [B, T, output_dim]
        trans_proj = self.trans_proj(trans_features)  # [B, T, output_dim]

        # 计算注意力权重
        concat_features = torch.cat([conv_proj, trans_proj], dim=-1)  # [B, T, output_dim*2]
        attention_weights = self.attention(concat_features)  # [B, T, 2]

        # 加权融合
        fused = (attention_weights[:, :, 0:1] * conv_proj +
                attention_weights[:, :, 1:2] * trans_proj)

        return self.layer_norm(fused)

def create_simplified_student_model(num_classes=1116, hidden_dim=512):
    """创建简化的学生模型"""
    return SimplifiedStudentModel(num_classes=num_classes, hidden_dim=hidden_dim)

def count_parameters(model):
    """统计模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

if __name__ == "__main__":
    # 创建学生模型
    student_model = StudentSignLanguageModel(num_classes=1116, hidden_dim=256)
    
    # 统计参数数量
    total_params = count_parameters(student_model)
    print(f"学生模型总参数数量: {total_params:,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 8, 112, 112)  # [batch_size, channels, frames, height, width]
    with torch.no_grad():
        output = student_model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"输出类别数: {output.shape[1]}")
    
    # 打印模型结构
    print("\n学生模型架构:")
    print(student_model)
