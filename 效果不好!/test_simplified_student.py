#!/usr/bin/env python3
"""
测试简化学生模型的基础功能
验证时序对齐和CTC兼容性
"""

import torch
import torch.nn.functional as F
import numpy as np
from student_model import create_simplified_student_model
from student_slr_network import StudentSLRModel

def test_model_shapes():
    """测试模型输入输出形状"""
    print("🔍 测试模型形状...")
    
    # 创建简化学生模型
    model = create_simplified_student_model(num_classes=1116, hidden_dim=512)
    model.eval()
    
    # 测试输入
    batch_size = 2
    T = 32  # 时序长度
    H, W = 112, 112
    
    x = torch.randn(batch_size, 3, T, H, W)
    print(f"输入形状: {x.shape}")
    
    with torch.no_grad():
        logits, features = model(x, return_features=True)
    
    print(f"输出logits形状: {logits.shape}")
    print(f"期望形状: [{batch_size}, {T}, 1116]")
    
    # 验证形状
    assert logits.shape == (batch_size, T, 1116), f"形状不匹配: {logits.shape}"
    print("✅ 形状测试通过!")
    
    return model, logits

def test_ctc_compatibility():
    """测试CTC兼容性"""
    print("\n🔍 测试CTC兼容性...")
    
    model, logits = test_model_shapes()
    batch_size, T, num_classes = logits.shape
    
    # 模拟标签 - 修复CTC格式
    label_lengths = torch.tensor([3, 5])  # 每个样本的标签长度
    total_labels = label_lengths.sum().item()
    labels = torch.randint(1, num_classes, (total_labels,))  # 连续的标签序列
    input_lengths = torch.full((batch_size,), T)  # 输入长度
    
    print(f"标签: {labels}")
    print(f"标签长度: {label_lengths}")
    print(f"输入长度: {input_lengths}")
    
    # 转换为CTC格式
    logits_ctc = logits.permute(1, 0, 2)  # [T, B, C]
    
    try:
        # 计算CTC损失
        ctc_loss = F.ctc_loss(
            logits_ctc.log_softmax(dim=-1),
            labels,
            input_lengths,
            label_lengths,
            blank=0,
            reduction='mean',
            zero_infinity=True
        )
        
        print(f"CTC损失: {ctc_loss.item():.4f}")
        print("✅ CTC兼容性测试通过!")
        
    except Exception as e:
        print(f"❌ CTC测试失败: {e}")
        return False
    
    return True

def test_slr_model_integration():
    """测试SLR模型集成"""
    print("\n🔍 测试SLR模型集成...")
    
    # 创建完整的SLR模型
    model = StudentSLRModel(
        num_classes=1116,
        c2d_type='student',
        conv_type=2,
        use_bn=True,
        hidden_size=512,
        gloss_dict=None,  # 暂时不使用解码器
        loss_weights={'SeqCTC': 1.0, 'ConvCTC': 0.0, 'Dist': 0.0},
        weight_norm=True,
        share_classifier=True
    )
    model.eval()
    
    # 测试输入
    batch_size = 2
    T = 32
    H, W = 112, 112
    
    vid = torch.randn(batch_size, T, 3, H, W)  # 原框架格式
    vid_lgt = torch.tensor([T, T])
    
    print(f"视频输入形状: {vid.shape}")
    print(f"视频长度: {vid_lgt}")
    
    with torch.no_grad():
        ret_dict = model(vid, vid_lgt)
    
    print(f"输出键: {ret_dict.keys()}")
    print(f"sequence_logits形状: {ret_dict['sequence_logits'].shape}")
    
    # 验证输出
    expected_shape = (batch_size, T, 1116)
    actual_shape = ret_dict['sequence_logits'].shape
    assert actual_shape == expected_shape, f"形状不匹配: {actual_shape} vs {expected_shape}"
    
    print("✅ SLR模型集成测试通过!")
    return True

def test_loss_computation():
    """测试损失计算"""
    print("\n🔍 测试损失计算...")
    
    # 创建模型
    model = StudentSLRModel(
        num_classes=1116,
        c2d_type='student',
        conv_type=2,
        use_bn=True,
        hidden_size=512,
        gloss_dict=None,
        loss_weights={'SeqCTC': 1.0, 'ConvCTC': 0.0, 'Dist': 0.0},
        weight_norm=True,
        share_classifier=True
    )
    model.train()
    
    # 测试数据
    batch_size = 2
    T = 32
    vid = torch.randn(batch_size, T, 3, 112, 112)
    vid_lgt = torch.tensor([T, T])
    
    # 模拟标签 - 修复CTC格式
    label_lengths = torch.tensor([2, 3])
    total_labels = label_lengths.sum().item()
    labels = torch.randint(1, 1116, (total_labels,))
    
    print(f"标签: {labels}")
    print(f"标签长度: {label_lengths}")
    
    # 前向传播
    ret_dict = model(vid, vid_lgt, label=labels, label_lgt=label_lengths)
    
    # 计算损失
    loss = model.criterion_calculation(ret_dict, labels, label_lengths)
    
    print(f"计算的损失: {loss.item():.4f}")
    
    # 验证损失
    assert not torch.isnan(loss), "损失为NaN"
    assert not torch.isinf(loss), "损失为无穷大"
    assert loss.item() > 0, "损失应该为正数"
    
    print("✅ 损失计算测试通过!")
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试简化学生模型...")
    
    try:
        # 运行所有测试
        test_model_shapes()
        test_ctc_compatibility()
        test_slr_model_integration()
        test_loss_computation()
        
        print("\n🎉 所有测试通过! 模型基础功能正常!")
        print("✅ 可以开始训练了!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
