<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="numpy" />
            <item index="2" class="java.lang.String" itemvalue="timm" />
            <item index="3" class="java.lang.String" itemvalue="fvcore" />
            <item index="4" class="java.lang.String" itemvalue="pandas" />
            <item index="5" class="java.lang.String" itemvalue="tqdm" />
            <item index="6" class="java.lang.String" itemvalue="PyYAML" />
            <item index="7" class="java.lang.String" itemvalue="matplotlib" />
            <item index="8" class="java.lang.String" itemvalue="opencv_python" />
            <item index="9" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>